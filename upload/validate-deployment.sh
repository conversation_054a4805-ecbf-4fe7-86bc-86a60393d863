#!/bin/bash

# ESS-HELM 部署验证脚本
# 版本: v2.1
# 基于: ESS-HELM 25.6.2 官方稳定版 (OCI格式)
# 功能: 验证部署包的完整性和配置正确性
# 作者: AI
# 日期: 2025-06-20

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 日志函数
info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 验证计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_item() {
    local description="$1"
    local command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查 $description... "
    
    if eval "$command" &>/dev/null; then
        echo -e "${GREEN}✓${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}✗${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 文件完整性验证
validate_file_structure() {
    info "验证文件结构完整性..."
    
    local required_files=(
        "setup.sh"
        "scripts/external.sh"
        "scripts/internal.sh"
        "scripts/admin.sh"
        "docs/readme.md"
        "docs/requirements.md"
        "docs/deployment-guide.md"
        "docs/admin-guide.md"
        "docs/troubleshooting.md"
    )
    
    for file in "${required_files[@]}"; do
        check_item "文件 $file 存在" "[[ -f '$PROJECT_DIR/$file' ]]"
    done
    
    # 检查脚本文件权限
    local script_files=(
        "setup.sh"
        "scripts/external.sh"
        "scripts/internal.sh"
        "scripts/admin.sh"
        "scripts/validate-deployment.sh"
    )
    
    for script in "${script_files[@]}"; do
        if [[ -f "$PROJECT_DIR/$script" ]]; then
            check_item "脚本 $script 可执行权限" "[[ -x '$PROJECT_DIR/$script' ]]"
        fi
    done
}

# 脚本语法验证
validate_script_syntax() {
    info "验证脚本语法正确性..."
    
    local script_files=(
        "setup.sh"
        "scripts/external.sh"
        "scripts/internal.sh"
        "scripts/admin.sh"
    )
    
    for script in "${script_files[@]}"; do
        if [[ -f "$PROJECT_DIR/$script" ]]; then
            check_item "脚本 $script 语法" "bash -n '$PROJECT_DIR/$script'"
        fi
    done
}

# 环境依赖验证
validate_environment_dependencies() {
    info "验证环境依赖..."
    
    local required_tools=(
        "curl"
        "wget"
        "git"
        "docker"
        "kubectl"
        "helm"
    )
    
    for tool in "${required_tools[@]}"; do
        check_item "工具 $tool 可用" "command -v $tool"
    done
    
    # 检查Helm版本是否支持OCI
    if command -v helm &>/dev/null; then
        local helm_version=$(helm version --short --client 2>/dev/null | grep -oE 'v[0-9]+\.[0-9]+' | sed 's/v//' || echo "0.0")
        local major_version=$(echo "$helm_version" | cut -d. -f1)
        local minor_version=$(echo "$helm_version" | cut -d. -f2)
        
        if [[ $major_version -ge 3 && $minor_version -ge 8 ]]; then
            check_item "Helm版本支持OCI ($helm_version)" "true"
        else
            check_item "Helm版本支持OCI ($helm_version)" "false"
        fi
    fi
}

# 网络连通性验证
validate_network_connectivity() {
    info "验证网络连通性..."
    
    check_item "GitHub连接" "curl -sSf https://github.com"
    check_item "GitHub Raw连接" "curl -sSf https://raw.githubusercontent.com"
    check_item "Element OCI仓库连接" "curl -sSf https://ghcr.io"
    
    # 验证OCI仓库访问
    if command -v helm &>/dev/null; then
        check_item "Element OCI Chart访问" "helm show chart oci://ghcr.io/element-hq/ess-helm/matrix-stack --version 25.6.2"
    fi
}

# 配置文件验证
validate_configuration_templates() {
    info "验证配置模板..."
    
    # 检查setup.sh中的关键配置
    check_item "setup.sh包含OCI支持" "grep -q 'OCI' '$PROJECT_DIR/setup.sh'"
    check_item "setup.sh包含Cloudflare配置" "grep -q 'cloudflare' '$PROJECT_DIR/setup.sh'"
    check_item "setup.sh包含Let's Encrypt配置" "grep -q 'letsencrypt' '$PROJECT_DIR/setup.sh'"
    
    # 检查internal.sh中的OCI配置
    check_item "internal.sh使用OCI格式" "grep -q 'oci://ghcr.io' '$PROJECT_DIR/scripts/internal.sh'"
    check_item "internal.sh移除传统repo" "! grep -q 'helm repo add' '$PROJECT_DIR/scripts/internal.sh'"
}

# 版本一致性验证
validate_version_consistency() {
    info "验证版本一致性..."
    
    local version_files=(
        "setup.sh"
        "scripts/internal.sh"
        "scripts/external.sh"
        "docs/readme.md"
        "docs/requirements.md"
    )
    
    for file in "${version_files[@]}"; do
        if [[ -f "$PROJECT_DIR/$file" ]]; then
            check_item "$file包含v2.1版本" "grep -q 'v2.1' '$PROJECT_DIR/$file'"
            check_item "$file包含OCI支持说明" "grep -q 'OCI' '$PROJECT_DIR/$file'"
        fi
    done
}

# 显示验证结果
show_validation_results() {
    echo
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${CYAN}验证结果摘要${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "总检查项: ${WHITE}$TOTAL_CHECKS${NC}"
    echo -e "通过检查: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "失败检查: ${RED}$FAILED_CHECKS${NC}"
    echo -e "成功率: ${WHITE}$(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        echo -e "${GREEN}🎉 所有验证项目都通过了！部署包已准备就绪。${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  有 $FAILED_CHECKS 个验证项目失败，请检查并修复后再进行部署。${NC}"
        return 1
    fi
}

# 主函数
main() {
    echo -e "${CYAN}ESS-HELM 部署包验证工具 v2.1${NC}"
    echo -e "${CYAN}基于: ESS-HELM 25.6.2 官方稳定版 (OCI格式)${NC}"
    echo
    
    validate_file_structure
    echo
    
    validate_script_syntax
    echo
    
    validate_environment_dependencies
    echo
    
    validate_network_connectivity
    echo
    
    validate_configuration_templates
    echo
    
    validate_version_consistency
    echo
    
    show_validation_results
}

# 启动验证
main "$@"
