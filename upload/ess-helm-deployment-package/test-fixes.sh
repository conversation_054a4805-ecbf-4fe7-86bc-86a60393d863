#!/bin/bash

# 测试修复后的ESS-HELM部署包脚本
# 验证邮箱收集和Kubernetes连接修复

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 日志函数
info() {
    echo -e "${CYAN}[测试]${NC} $1"
}

success() {
    echo -e "${GREEN}[通过]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

error() {
    echo -e "${RED}[失败]${NC} $1"
}

# 测试邮箱收集逻辑
test_email_collection() {
    info "测试邮箱收集逻辑..."
    
    # 检查setup.sh中的邮箱收集函数
    if grep -q "acme@\${MAIN_DOMAIN" upload/ess-helm-deployment-package/setup.sh; then
        success "setup.sh: 邮箱默认值修复正确"
    else
        error "setup.sh: 邮箱默认值修复失败"
        return 1
    fi
    
    # 检查是否包含重要说明
    if grep -q "建议使用专门用于证书管理的邮箱地址" upload/ess-helm-deployment-package/setup.sh; then
        success "setup.sh: 邮箱说明添加正确"
    else
        error "setup.sh: 邮箱说明添加失败"
        return 1
    fi
    
    # 检查external.sh中的邮箱使用
    if grep -q "cert_email=\"\${LETSENCRYPT_EMAIL:-acme@\$MAIN_DOMAIN}\"" upload/ess-helm-deployment-package/scripts/external.sh; then
        success "external.sh: 邮箱使用逻辑修复正确"
    else
        error "external.sh: 邮箱使用逻辑修复失败"
        return 1
    fi
    
    return 0
}

# 测试Kubernetes连接修复
test_kubernetes_fixes() {
    info "测试Kubernetes连接修复..."
    
    # 检查internal.sh中的详细诊断
    if grep -q "诊断信息:" upload/ess-helm-deployment-package/scripts/internal.sh; then
        success "internal.sh: 诊断信息添加正确"
    else
        error "internal.sh: 诊断信息添加失败"
        return 1
    fi
    
    # 检查自动修复选项
    if grep -q "自动修复选项:" upload/ess-helm-deployment-package/scripts/internal.sh; then
        success "internal.sh: 自动修复选项添加正确"
    else
        error "internal.sh: 自动修复选项添加失败"
        return 1
    fi
    
    # 检查setup-kubernetes.sh脚本是否存在
    if [[ -f "upload/ess-helm-deployment-package/scripts/setup-kubernetes.sh" ]]; then
        success "setup-kubernetes.sh: 脚本创建成功"
    else
        error "setup-kubernetes.sh: 脚本创建失败"
        return 1
    fi
    
    # 检查脚本是否可执行
    if [[ -x "upload/ess-helm-deployment-package/scripts/setup-kubernetes.sh" ]]; then
        success "setup-kubernetes.sh: 执行权限设置正确"
    else
        error "setup-kubernetes.sh: 执行权限设置失败"
        return 1
    fi
    
    return 0
}

# 测试脚本语法
test_script_syntax() {
    info "测试脚本语法..."
    
    local scripts=(
        "upload/ess-helm-deployment-package/setup.sh"
        "upload/ess-helm-deployment-package/scripts/external.sh"
        "upload/ess-helm-deployment-package/scripts/internal.sh"
        "upload/ess-helm-deployment-package/scripts/setup-kubernetes.sh"
    )
    
    for script in "${scripts[@]}"; do
        if bash -n "$script" 2>/dev/null; then
            success "$(basename "$script"): 语法检查通过"
        else
            error "$(basename "$script"): 语法检查失败"
            bash -n "$script"
            return 1
        fi
    done
    
    return 0
}

# 显示修复摘要
show_fix_summary() {
    echo
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                    修复摘要                                  ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${YELLOW}1. 邮箱收集逻辑修复:${NC}"
    echo -e "   • 修改setup.sh提供更好的默认邮箱建议 (acme@域名)"
    echo -e "   • 添加邮箱用途说明，明确仅用于证书管理"
    echo -e "   • 修复external.sh使用用户提供的邮箱而非硬编码"
    echo
    echo -e "${YELLOW}2. Kubernetes连接错误修复:${NC}"
    echo -e "   • 增强internal.sh的错误诊断信息"
    echo -e "   • 添加自动修复选项"
    echo -e "   • 创建setup-kubernetes.sh脚本支持自动安装k3s/microk8s"
    echo
    echo -e "${YELLOW}3. 根本原因分析:${NC}"
    echo -e "   • 服务器缺少~/.kube/config文件"
    echo -e "   • kubectl使用默认配置连接localhost:8080"
    echo -e "   • 需要安装和配置Kubernetes集群"
    echo
}

# 主测试函数
main() {
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                ESS-HELM修复验证测试                          ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    local test_passed=0
    local test_total=0
    
    # 测试邮箱收集逻辑
    ((test_total++))
    if test_email_collection; then
        ((test_passed++))
    fi
    echo
    
    # 测试Kubernetes连接修复
    ((test_total++))
    if test_kubernetes_fixes; then
        ((test_passed++))
    fi
    echo
    
    # 测试脚本语法
    ((test_total++))
    if test_script_syntax; then
        ((test_passed++))
    fi
    echo
    
    # 显示测试结果
    echo -e "${CYAN}测试结果: ${test_passed}/${test_total} 通过${NC}"
    
    if [[ $test_passed -eq $test_total ]]; then
        success "所有测试通过！修复成功完成。"
        show_fix_summary
        return 0
    else
        error "部分测试失败，请检查修复内容。"
        return 1
    fi
}

# 运行测试
main "$@"
