# ESS-HELM 部署包需求规范文档

## 📋 文档概述

**文档版本**: v2.1  
**基于**: ESS-HELM 25.6.2 官方稳定版  
**更新时间**: 2025-06-20  
**适用范围**: ESS-HELM一键部署系统

本文档定义了ESS-HELM部署包的完整需求规范，确保部署包符合Element官方标准、OCI规范要求，并满足生产环境的部署和运维需求。

## 🎯 核心需求

### 1. 官方标准合规性

#### 1.1 ESS-HELM版本要求
- **✅ 必须**: 基于Element官方ESS-HELM 25.6.2稳定版
- **✅ 必须**: 严格遵循官方API和配置规范
- **✅ 必须**: 不得修改官方Chart的核心逻辑
- **❌ 禁止**: 使用开发版本或非官方分支

#### 1.2 OCI (Open Container Initiative) 规范要求 🆕
- **✅ 必须**: 使用官方OCI格式仓库地址 `oci://ghcr.io/element-hq/ess-helm/matrix-stack`
- **✅ 必须**: 移除传统的`helm repo add`和`helm pull`方式
- **✅ 必须**: 直接使用OCI地址进行Helm部署
- **✅ 必须**: 支持Helm 3.8+版本 (OCI支持要求)
- **✅ 必须**: 验证OCI仓库访问性和网络连通性
- **❌ 禁止**: 使用过时的传统Helm仓库方式

#### 1.3 版本兼容性验证
- **✅ 必须**: 部署前验证Helm版本支持OCI
- **✅ 必须**: 验证Kubernetes版本兼容性
- **✅ 必须**: 检查网络访问ghcr.io的能力

### 2. 架构设计要求

#### 2.1 服务器角色定义
- **外部服务器**: 仅运行NGINX反向代理 (指路牌功能)
- **内部服务器**: 运行完整Matrix服务栈
- **❌ 禁止**: 外部服务器运行完整Matrix服务
- **❌ 禁止**: 内部服务器承担外部路由功能

#### 2.2 网络架构要求
- **✅ 必须**: 外部服务器实现反向代理到内部服务器
- **✅ 必须**: 内部服务器提供实际Matrix服务
- **✅ 必须**: 支持Router WAN IP自动检测
- **✅ 必须**: 支持虚拟公网IP路由管理

### 3. 部署方式要求

#### 3.1 一键部署支持
- **✅ 必须**: 支持`bash <(curl -sSL <URL>/setup.sh)`方式部署
- **✅ 必须**: 检测执行方式并自动下载完整项目
- **✅ 必须**: 修复bash进程替换问题
- **❌ 禁止**: 要求用户改变使用习惯

#### 3.2 独立部署能力
- **✅ 必须**: 支持在全新Git仓库中完全独立部署
- **✅ 必须**: 包含所有必需的核心文件
- **✅ 必须**: 不依赖硬编码路径
- **✅ 必须**: 完整的自包含性

### 4. 用户体验要求

#### 4.1 中文交互界面
- **✅ 必须**: 提供用户友好的中文交互式菜单
- **✅ 必须**: 支持选项'0'进行导航
- **✅ 必须**: 智能默认值配置
- **✅ 必须**: 技术小白友好设计

#### 4.2 配置管理
- **✅ 必须**: 支持自定义域名、子域名配置
- **✅ 必须**: 支持外部服务端口配置
- **✅ 必须**: 支持服务主目录配置
- **✅ 必须**: 配置验证和错误提示

### 5. 技术规范要求

#### 5.1 脚本架构
- **✅ 必须**: 使用标准化脚本架构 (setup.sh, external.sh, internal.sh, admin.sh)
- **✅ 必须**: 标准化临时文件处理 (使用/tmp/目录和mktemp)
- **✅ 必须**: 正确的日志文件管理和复制机制
- **✅ 必须**: 修复文件路径错误

#### 5.2 文件结构标准
- **✅ 必须**: 遵循上游目录结构标准
- **✅ 必须**: 移除非必要文件
- **✅ 必须**: 保持核心文件完整性
- **✅ 必须**: 正确的配置语法

### 6. 生产环境要求

#### 6.1 部署验证
- **✅ 必须**: 部署前检查 (环境、依赖、网络)
- **✅ 必须**: 部署过程验证 (状态监控、错误处理)
- **✅ 必须**: 服务功能测试 (连通性、API可用性)
- **✅ 必须**: 稳定性和性能评估

#### 6.2 文档完整性
- **✅ 必须**: 提供完整的部署文档
- **✅ 必须**: 包含故障排除指南
- **✅ 必须**: 管理和维护说明
- **✅ 必须**: 最佳实践建议

## 🔧 技术实现要求

### 1. OCI规范实现细节

#### 1.1 Chart获取方式更新
```bash
# ❌ 旧方式 (不符合OCI规范)
helm repo add element-hq https://element-hq.github.io/ess-helm
helm pull element-hq/matrix-stack --version "$ESS_HELM_VERSION"

# ✅ 新方式 (符合OCI规范)
helm upgrade --install "$RELEASE_NAME" "oci://ghcr.io/element-hq/ess-helm/matrix-stack" \
    --version "$ESS_HELM_VERSION"
```

#### 1.2 版本验证要求
```bash
# ✅ 必须验证Helm版本支持OCI
local helm_version=$(helm version --short --client | grep -oE 'v[0-9]+\.[0-9]+')
if [[ $major_version -lt 3 ]] || [[ $major_version -eq 3 && $minor_version -lt 8 ]]; then
    error "Helm版本不支持OCI，需要3.8或更高版本"
fi
```

### 2. 部署流程优化

#### 2.1 简化的部署流程
1. **环境验证** → **OCI访问验证** → **配置生成** → **直接OCI部署**
2. **移除**: 传统仓库添加、更新、下载步骤
3. **增加**: OCI网络连通性检查

#### 2.2 错误处理增强
- **✅ 必须**: OCI访问失败时的明确错误提示
- **✅ 必须**: Helm版本不兼容时的升级指导
- **✅ 必须**: 网络问题的诊断建议

## 📊 验收标准

### 1. 功能验收
- [ ] 支持OCI格式Chart部署
- [ ] 一键部署功能正常
- [ ] 中文交互界面完整
- [ ] 配置验证机制有效
- [ ] 错误处理机制完善

### 2. 性能验收
- [ ] 部署时间优化 (相比传统方式)
- [ ] 资源使用合理
- [ ] 网络访问效率提升

### 3. 兼容性验收
- [ ] Helm 3.8+ 版本兼容
- [ ] Kubernetes 1.20+ 兼容
- [ ] 多操作系统支持

### 4. 安全验收
- [ ] OCI签名验证
- [ ] 网络安全配置
- [ ] 访问控制机制

## 🔄 持续改进要求

### 1. 版本跟踪
- **✅ 必须**: 跟踪Element官方ESS-HELM版本更新
- **✅ 必须**: 及时适配新的OCI规范变更
- **✅ 必须**: 保持与官方标准的同步

### 2. 社区反馈
- **✅ 必须**: 收集用户反馈和问题报告
- **✅ 必须**: 持续优化用户体验
- **✅ 必须**: 定期更新文档和最佳实践

---

**注**: 本需求文档将根据Element官方标准更新和社区反馈持续优化。所有实现必须严格遵循本文档的要求规范。
