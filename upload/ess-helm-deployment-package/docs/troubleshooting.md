# ESS-HELM 故障排除指南

## 📋 概述

本指南提供ESS-HELM部署和运行过程中常见问题的解决方案。涵盖部署问题、服务故障、网络问题、性能问题等各种场景。

**版本**: v1.0  
**基于**: ESS-HELM 25.6.2 官方稳定版  
**更新时间**: 2025-06-20

## 🚨 紧急故障处理

### 服务完全不可用

```bash
# 1. 快速检查集群状态
kubectl get nodes
kubectl get pods -n matrix

# 2. 检查关键服务
kubectl get pods -n matrix -l app.kubernetes.io/name=synapse
kubectl get pods -n matrix -l app.kubernetes.io/name=postgresql

# 3. 查看最近事件
kubectl get events -n matrix --sort-by='.lastTimestamp' | tail -20

# 4. 紧急重启服务
kubectl rollout restart deployment/synapse -n matrix
kubectl rollout restart deployment/element-web -n matrix
```

### 数据库连接失败

```bash
# 1. 检查PostgreSQL状态
kubectl get pods -n matrix -l app.kubernetes.io/name=postgresql

# 2. 查看数据库日志
kubectl logs -f deployment/postgresql -n matrix

# 3. 测试数据库连接
kubectl exec -it deployment/postgresql -n matrix -- psql -U synapse -d synapse -c "SELECT 1;"

# 4. 重启数据库 (谨慎操作)
kubectl rollout restart statefulset/postgresql -n matrix
```

## 🔧 部署问题

### 问题1: Helm部署失败

**症状**: Helm install/upgrade命令失败

**诊断步骤**:
```bash
# 检查Helm状态
helm list -n matrix
helm status matrix-stack -n matrix

# 查看详细错误
helm get notes matrix-stack -n matrix
helm get values matrix-stack -n matrix
```

**解决方案**:
```bash
# 1. 检查配置文件语法
helm template matrix-stack ./charts/matrix-stack \
  --values ./configs/values.yaml \
  --dry-run

# 2. 清理失败的部署
helm uninstall matrix-stack -n matrix

# 3. 重新部署
helm install matrix-stack ./charts/matrix-stack \
  --namespace matrix \
  --values ./configs/values.yaml \
  --timeout 20m
```

### 问题2: Pod启动失败

**症状**: Pod处于Pending、CrashLoopBackOff或Error状态

**诊断步骤**:
```bash
# 查看Pod状态
kubectl get pods -n matrix -o wide

# 查看Pod详情
kubectl describe pod <pod-name> -n matrix

# 查看Pod日志
kubectl logs <pod-name> -n matrix --previous
```

**常见原因和解决方案**:

#### 资源不足
```bash
# 检查节点资源
kubectl top nodes
kubectl describe nodes

# 解决方案: 调整资源请求或增加节点
# 编辑 values.yaml
resources:
  requests:
    memory: "256Mi"  # 降低内存请求
    cpu: "100m"      # 降低CPU请求
```

#### 镜像拉取失败
```bash
# 检查镜像拉取状态
kubectl describe pod <pod-name> -n matrix | grep -A 10 "Events:"

# 解决方案: 检查镜像名称和网络连接
# 手动拉取镜像测试
docker pull matrixdotorg/synapse:v1.97.0
```

#### 配置错误
```bash
# 检查ConfigMap和Secret
kubectl get configmap -n matrix
kubectl get secret -n matrix

# 查看配置内容
kubectl describe configmap <configmap-name> -n matrix
```

### 问题3: 存储问题

**症状**: PVC处于Pending状态或存储空间不足

**诊断步骤**:
```bash
# 检查PVC状态
kubectl get pvc -n matrix

# 查看存储类
kubectl get storageclass

# 检查存储使用情况
kubectl exec -n matrix deployment/synapse -- df -h /data
```

**解决方案**:
```bash
# 1. 检查存储类配置
kubectl describe storageclass <storage-class-name>

# 2. 扩展PVC (如果支持)
kubectl patch pvc <pvc-name> -n matrix -p '{"spec":{"resources":{"requests":{"storage":"50Gi"}}}}'

# 3. 清理旧数据
kubectl exec -n matrix deployment/synapse -- find /data -name "*.log.*" -mtime +7 -delete
```

## 🌐 网络问题

### 问题1: 服务无法访问

**症状**: 无法通过域名访问服务

**诊断步骤**:
```bash
# 检查Ingress状态
kubectl get ingress -n matrix

# 检查Service状态
kubectl get services -n matrix

# 检查Endpoints
kubectl get endpoints -n matrix

# 测试内部连接
kubectl exec -n matrix deployment/synapse -- curl -I http://element-web:80
```

**解决方案**:
```bash
# 1. 检查DNS解析
nslookup matrix.yourdomain.com

# 2. 检查Ingress Controller
kubectl get pods -n ingress-nginx

# 3. 检查证书
kubectl get secret -n matrix | grep tls
kubectl describe secret matrix-tls -n matrix
```

### 问题2: 联邦连接失败

**症状**: 无法与其他Matrix服务器联邦

**诊断步骤**:
```bash
# 测试联邦端点
curl -f https://matrix.yourdomain.com:8448/_matrix/federation/v1/version

# 检查防火墙
sudo ufw status
sudo iptables -L

# 检查端口监听
netstat -tlnp | grep 8448
```

**解决方案**:
```bash
# 1. 开放联邦端口
sudo ufw allow 8448/tcp

# 2. 检查well-known配置
curl https://yourdomain.com/.well-known/matrix/server

# 3. 验证证书
openssl s_client -connect matrix.yourdomain.com:8448 -servername matrix.yourdomain.com
```

### 问题3: TURN服务问题

**症状**: 音视频通话无法建立

**诊断步骤**:
```bash
# 检查TURN服务状态
kubectl get pods -n matrix -l app.kubernetes.io/name=turn

# 测试TURN连接
telnet turn.yourdomain.com 3478

# 检查TURN配置
kubectl logs deployment/turn -n matrix
```

**解决方案**:
```bash
# 1. 检查TURN端口
sudo ufw allow 3478/udp
sudo ufw allow 5349/tcp
sudo ufw allow 30152:33152/udp

# 2. 验证TURN配置
# 编辑 values.yaml 中的TURN配置
turn_uris:
  - "turn:turn.yourdomain.com:3478?transport=udp"
  - "turns:turn.yourdomain.com:5349?transport=tcp"
```

## 🔐 认证和权限问题

### 问题1: 无法登录

**症状**: 用户无法登录Matrix服务

**诊断步骤**:
```bash
# 检查Synapse日志
kubectl logs deployment/synapse -n matrix | grep -i "login\|auth"

# 测试登录API
curl -X POST "https://matrix.yourdomain.com/_matrix/client/r0/login" \
  -H "Content-Type: application/json" \
  -d '{"type":"m.login.password","user":"test","password":"test"}'
```

**解决方案**:
```bash
# 1. 检查用户是否存在
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v2/users/@user:yourdomain.com" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 2. 重置用户密码
./scripts/admin.sh
# 选择 "1. 用户管理" -> "3. 重置用户密码"

# 3. 检查认证配置
kubectl get configmap synapse-config -n matrix -o yaml
```

### 问题2: 管理员API无法访问

**症状**: 无法访问Synapse管理API

**诊断步骤**:
```bash
# 测试管理API
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v1/server_version" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 检查访问令牌
# 确保令牌是管理员用户的有效令牌
```

**解决方案**:
```bash
# 1. 生成新的访问令牌
# 登录Element Web，在设置中生成新令牌

# 2. 确认用户是管理员
curl -X GET "https://matrix.yourdomain.com/_synapse/admin/v2/users/@admin:yourdomain.com" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 3. 检查网络策略
kubectl get networkpolicy -n matrix
```

## 📊 性能问题

### 问题1: 响应缓慢

**症状**: 服务响应时间长，用户体验差

**诊断步骤**:
```bash
# 检查资源使用
kubectl top pods -n matrix
kubectl top nodes

# 检查数据库性能
kubectl exec -it deployment/postgresql -n matrix -- psql -U synapse -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"
```

**解决方案**:
```bash
# 1. 增加资源限制
# 编辑 values.yaml
resources:
  limits:
    memory: "4Gi"
    cpu: "2000m"

# 2. 优化数据库
kubectl exec -it deployment/postgresql -n matrix -- psql -U synapse -c "
REINDEX DATABASE synapse;
ANALYZE;"

# 3. 启用缓存
# 在 values.yaml 中配置缓存
synapse:
  config:
    caches:
      global_factor: 2.0
```

### 问题2: 内存使用过高

**症状**: Pod因内存不足被杀死

**诊断步骤**:
```bash
# 查看Pod重启历史
kubectl get pods -n matrix -o wide

# 检查内存使用
kubectl exec -n matrix deployment/synapse -- cat /proc/meminfo
kubectl exec -n matrix deployment/synapse -- ps aux --sort=-%mem | head -10
```

**解决方案**:
```bash
# 1. 增加内存限制
resources:
  limits:
    memory: "8Gi"

# 2. 优化Synapse配置
synapse:
  config:
    gc_thresholds: [700, 10, 10]
    gc_min_interval: [0.5, 30.0, 1.0]

# 3. 启用Worker模式
workers:
  enabled: true
  count: 2
```

## 🔄 Router WAN IP检测问题

### 问题1: 无法连接RouterOS

**症状**: Router WAN IP检测服务无法连接到RouterOS设备

**诊断步骤**:
```bash
# 检查检测服务状态
./scripts/router-wan-ip-detector.sh --status

# 测试RouterOS连接
nc -zv *********** 8728

# 查看检测日志
tail -f ~/matrix/router-wan-ip-detection.log
```

**解决方案**:
```bash
# 1. 检查RouterOS API配置
# 在RouterOS中启用API服务
/ip service enable api
/ip service set api port=8728

# 2. 检查防火墙规则
# 确保允许从Kubernetes集群访问RouterOS

# 3. 验证认证信息
./scripts/router-wan-ip-detector.sh \
  --router-ip *********** \
  --router-username admin \
  --router-password your-password \
  --test
```

### 问题2: WAN IP检测不准确

**症状**: 检测到的WAN IP地址不正确

**诊断步骤**:
```bash
# 检查WAN接口配置
# 在RouterOS中查看接口状态
/interface print

# 查看当前检测到的IP
cat ~/matrix/current-wan-ip.txt

# 手动验证WAN IP
curl ifconfig.me
```

**解决方案**:
```bash
# 1. 指定正确的WAN接口
./scripts/router-wan-ip-detector.sh \
  --wan-interface pppoe-out1  # 或其他正确的接口名

# 2. 检查接口配置
# 确保RouterOS中WAN接口配置正确

# 3. 调整检测逻辑
# 编辑检测脚本，添加多接口支持
```

## 🔀 虚拟IP路由问题

### 问题1: 虚拟IP路由不生效

**症状**: 虚拟IP地址无法访问

**诊断步骤**:
```bash
# 检查路由管理服务状态
./scripts/virtual-public-ip-route-manager.sh --status

# 查看iptables规则
sudo iptables -t nat -L PREROUTING -n | grep -E "(**********|**********)"

# 测试虚拟IP连通性
ping -c 3 **********
ping -c 3 **********
```

**解决方案**:
```bash
# 1. 手动创建路由规则
sudo iptables -t nat -A PREROUTING -d ********** -j DNAT --to-destination YOUR_WAN_IP
sudo iptables -t nat -A POSTROUTING -s YOUR_WAN_IP -j SNAT --to-source **********

# 2. 重启路由管理服务
./scripts/virtual-public-ip-route-manager.sh --stop
./scripts/virtual-public-ip-route-manager.sh --daemon

# 3. 检查权限
# 确保脚本有足够权限操作iptables
```

### 问题2: 路由切换不及时

**症状**: WAN IP变化后，虚拟IP路由更新延迟

**诊断步骤**:
```bash
# 查看路由管理日志
tail -f ~/matrix/virtual-ip-routing.log

# 检查更新间隔配置
grep "UPDATE_INTERVAL" ~/matrix/virtual-ip-config.json
```

**解决方案**:
```bash
# 1. 调整更新间隔
./scripts/virtual-public-ip-route-manager.sh \
  --update-interval 5  # 5秒更新间隔

# 2. 手动触发更新
./scripts/virtual-public-ip-route-manager.sh \
  --update-wan-ip NEW_WAN_IP

# 3. 优化检测逻辑
# 编辑路由管理脚本，改进检测算法
```

## 🛠️ 诊断工具

### 系统诊断脚本

```bash
#!/bin/bash
# diagnosis.sh - 系统诊断脚本

echo "=== ESS-HELM 系统诊断 ==="
echo "时间: $(date)"
echo

echo "=== Kubernetes集群状态 ==="
kubectl cluster-info
kubectl get nodes
echo

echo "=== Matrix命名空间状态 ==="
kubectl get all -n matrix
echo

echo "=== 存储状态 ==="
kubectl get pvc -n matrix
echo

echo "=== 网络状态 ==="
kubectl get ingress -n matrix
kubectl get networkpolicy -n matrix
echo

echo "=== 最近事件 ==="
kubectl get events -n matrix --sort-by='.lastTimestamp' | tail -10
echo

echo "=== 资源使用 ==="
kubectl top nodes 2>/dev/null || echo "需要安装metrics-server"
kubectl top pods -n matrix 2>/dev/null || echo "需要安装metrics-server"
echo

echo "=== 服务连通性测试 ==="
curl -f -s https://matrix.yourdomain.com/_matrix/client/versions && echo "Matrix API: OK" || echo "Matrix API: FAIL"
curl -f -s https://element.yourdomain.com/ && echo "Element Web: OK" || echo "Element Web: FAIL"
echo

echo "=== 诊断完成 ==="
```

### 日志收集脚本

```bash
#!/bin/bash
# collect-logs.sh - 日志收集脚本

LOG_DIR="logs-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$LOG_DIR"

echo "收集系统日志到: $LOG_DIR"

# 收集Pod日志
for pod in $(kubectl get pods -n matrix -o name); do
    pod_name=$(echo $pod | cut -d'/' -f2)
    kubectl logs $pod -n matrix > "$LOG_DIR/${pod_name}.log" 2>&1
    kubectl logs $pod -n matrix --previous > "$LOG_DIR/${pod_name}-previous.log" 2>&1 || true
done

# 收集配置信息
kubectl get all -n matrix -o yaml > "$LOG_DIR/resources.yaml"
kubectl describe pods -n matrix > "$LOG_DIR/pod-descriptions.txt"
kubectl get events -n matrix > "$LOG_DIR/events.txt"

# 收集自定义日志
cp ~/matrix/*.log "$LOG_DIR/" 2>/dev/null || true

# 打包日志
tar -czf "${LOG_DIR}.tar.gz" "$LOG_DIR"
echo "日志已打包: ${LOG_DIR}.tar.gz"
```

## 📞 获取支持

### 自助诊断

1. **运行诊断脚本**
   ```bash
   ./diagnosis.sh > diagnosis-report.txt
   ```

2. **收集日志**
   ```bash
   ./collect-logs.sh
   ```

3. **查看官方文档**
   - [ESS-HELM官方文档](https://github.com/element-hq/ess-helm)
   - [Synapse文档](https://matrix-org.github.io/synapse/)

### 社区支持

- **Matrix社区**: #synapse:matrix.org
- **GitHub Issues**: 提交详细的问题报告
- **官方论坛**: Element社区论坛

### 问题报告模板

```
**环境信息**:
- ESS-HELM版本: 25.6.2
- Kubernetes版本: 
- 操作系统: 
- 部署模式: 外部/内部

**问题描述**:
[详细描述问题现象]

**重现步骤**:
1. 
2. 
3. 

**期望结果**:
[描述期望的正常行为]

**实际结果**:
[描述实际发生的情况]

**日志信息**:
[粘贴相关日志]

**诊断信息**:
[粘贴诊断脚本输出]
```

---

**注意**: 在寻求支持时，请确保不要泄露敏感信息如密码、访问令牌等。
