# 虚拟公网IP路由高可用配置
# 版本: v1.0
# 功能: 仅为需要外部直接连接的服务配置虚拟IP
# 支持: LiveKit (**********) + TURN (**********)

virtualPublicIpRouting:
  enabled: true
  
  # LiveKit服务虚拟IP配置
  livekit:
    virtualPublicIp: "**********"
    enabled: true
    routingMode: "dynamic"
    
    # 健康检查配置
    healthCheck:
      enabled: true
      interval: "30s"
      timeout: "10s"
      retryAttempts: 3
      
    # 服务端口配置
    ports:
      - name: "livekit-http"
        port: 7880
        protocol: "TCP"
      - name: "livekit-rtc"
        port: 7881
        protocol: "UDP"
      - name: "livekit-turn"
        port: 3478
        protocol: "UDP"
        
    # 负载均衡配置
    loadBalancing:
      enabled: true
      algorithm: "round-robin"
      sessionAffinity: true
      
  # TURN服务虚拟IP配置
  turn:
    virtualPublicIp: "**********"
    enabled: true
    routingMode: "dynamic"
    
    # 健康检查配置
    healthCheck:
      enabled: true
      interval: "30s"
      timeout: "10s"
      retryAttempts: 3
      
    # 服务端口配置
    ports:
      - name: "turn-udp"
        port: 3478
        protocol: "UDP"
      - name: "turn-tcp"
        port: 3478
        protocol: "TCP"
      - name: "turn-tls"
        port: 5349
        protocol: "TCP"
      - name: "turn-dtls"
        port: 5349
        protocol: "UDP"
        
    # 端口范围配置
    portRanges:
      - name: "turn-relay"
        startPort: 30152
        endPort: 33152
        protocol: "UDP"
        
    # 负载均衡配置
    loadBalancing:
      enabled: true
      algorithm: "least-connections"
      sessionAffinity: true
      
  # ESS内部服务自动处理，无需虚拟IP配置
  synapse:
    useVirtualIp: false  # 由ESS-HELM自动处理
    internalRouting: true
    
  elementWeb:
    useVirtualIp: false  # 由ESS-HELM自动处理
    internalRouting: true
    
  mas:
    useVirtualIp: false  # 由ESS-HELM自动处理
    internalRouting: true
    
  # 路由管理配置
  routeManager:
    enabled: true
    updateInterval: "10s"
    failoverTimeout: "60s"
    
    # 路由表管理
    routingTable:
      enabled: true
      autoCleanup: true
      maxEntries: 1000
      
    # 路由策略
    routingPolicy:
      enableSourceRouting: true
      enableDestinationRouting: true
      enablePortBasedRouting: true
      
  # 零停机切换配置
  zeroDowntime:
    enabled: true
    gracefulSwitchTimeout: "30s"
    
    # 切换策略
    switchStrategy:
      mode: "graceful"
      drainTimeout: "30s"
      maxConcurrentSwitches: 1
      
    # 回滚配置
    rollback:
      enabled: true
      autoRollback: true
      rollbackTimeout: "60s"
      
  # 网络配置
  networking:
    # IP转发配置
    ipForwarding:
      enabled: true
      ipv4: true
      ipv6: false
      
    # NAT配置
    nat:
      enabled: true
      masquerade: true
      preserveSourcePort: true
      
    # 防火墙配置
    firewall:
      enabled: true
      allowEstablished: true
      allowRelated: true
      dropInvalid: true
      
  # 监控配置
  monitoring:
    enabled: true
    
    # 指标收集
    metrics:
      enabled: true
      interval: "15s"
      exportPrometheus: true
      
    # 日志配置
    logging:
      level: "INFO"
      enableDebug: false
      logRotation:
        enabled: true
        maxSize: "50MB"
        maxFiles: 10
        
    # 告警配置
    alerting:
      enabled: true
      thresholds:
        failoverTime: "30s"
        errorRate: 0.05
        latency: "100ms"

# 相关服务配置
services:
  # 虚拟IP路由管理服务
  virtualIpRouteManager:
    enabled: true
    image:
      repository: "alpine"
      tag: "latest"
      pullPolicy: "IfNotPresent"
      
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"
        
    # 特权模式 (路由操作需要)
    securityContext:
      privileged: true
      runAsUser: 0
      
    # 环境变量
    env:
      - name: LIVEKIT_VIRTUAL_IP
        value: "**********"
      - name: TURN_VIRTUAL_IP
        value: "**********"
      - name: UPDATE_INTERVAL
        value: "10"
      - name: GRACEFUL_SWITCH_TIMEOUT
        value: "30"
        
    # 网络配置
    networking:
      hostNetwork: true  # 需要访问主机网络进行路由操作
      dnsPolicy: "ClusterFirstWithHostNet"
      
    # 存储配置
    persistence:
      enabled: true
      size: "1Gi"
      storageClass: ""
      accessMode: "ReadWriteOnce"
      
    # 生命周期钩子
    lifecycle:
      preStop:
        exec:
          command:
            - "/bin/sh"
            - "-c"
            - "kill -TERM $(cat /tmp/route-manager.pid)"
            
    # 健康检查
    livenessProbe:
      exec:
        command:
          - "/bin/sh"
          - "-c"
          - "test -f /tmp/route-manager.pid && kill -0 $(cat /tmp/route-manager.pid)"
      initialDelaySeconds: 30
      periodSeconds: 30
      timeoutSeconds: 5
      failureThreshold: 3
      
    readinessProbe:
      exec:
        command:
          - "/bin/sh"
          - "-c"
          - "iptables -t nat -L PREROUTING -n | grep -E '(**********|**********)'"
      initialDelaySeconds: 15
      periodSeconds: 15
      timeoutSeconds: 5
      failureThreshold: 3

# ConfigMap配置
configMaps:
  virtualIpRouting:
    enabled: true
    data:
      config.yaml: |
        livekit:
          virtualIp: "**********"
          enabled: true
          routingMode: "dynamic"
        turn:
          virtualIp: "**********"
          enabled: true
          routingMode: "dynamic"
        routeManager:
          updateInterval: "10s"
          failoverTimeout: "60s"
        zeroDowntime:
          enabled: true
          gracefulSwitchTimeout: "30s"

# RBAC配置
rbac:
  create: true
  rules:
    - apiGroups: [""]
      resources: ["configmaps"]
      verbs: ["get", "list", "watch", "create", "update", "patch"]
    - apiGroups: [""]
      resources: ["services"]
      verbs: ["get", "list", "watch", "update", "patch"]
    - apiGroups: [""]
      resources: ["endpoints"]
      verbs: ["get", "list", "watch"]
    - apiGroups: [""]
      resources: ["events"]
      verbs: ["create"]

# 网络策略
networkPolicy:
  enabled: true
  policyTypes:
    - Ingress
    - Egress
  egress:
    # 允许所有出站流量 (路由管理需要)
    - {}
  ingress:
    # 允许来自同一命名空间的访问
    - from:
        - namespaceSelector:
            matchLabels:
              name: "matrix"
    # 允许来自虚拟IP的访问
    - from: []
      ports:
        - protocol: TCP
          port: 7880
        - protocol: UDP
          port: 7881
        - protocol: UDP
          port: 3478
        - protocol: TCP
          port: 3478
        - protocol: TCP
          port: 5349
        - protocol: UDP
          port: 5349

# 服务监控
serviceMonitor:
  enabled: false
  interval: "30s"
  scrapeTimeout: "10s"
  labels: {}
  annotations: {}

# Pod监控
podMonitor:
  enabled: false
  interval: "30s"
  scrapeTimeout: "10s"
  labels: {}
  annotations: {}

# 告警规则
prometheusRule:
  enabled: false
  groups:
    - name: "virtual-ip-routing"
      rules:
        - alert: "VirtualIpRoutingDown"
          expr: 'up{job="virtual-ip-route-manager"} == 0'
          for: "5m"
          labels:
            severity: "critical"
          annotations:
            summary: "虚拟IP路由管理服务已停止"
            description: "虚拟IP路由管理服务已停止超过5分钟"
            
        - alert: "VirtualIpFailover"
          expr: 'increase(virtual_ip_failover_total[5m]) > 0'
          for: "0m"
          labels:
            severity: "warning"
          annotations:
            summary: "虚拟IP故障转移"
            description: "在过去5分钟内发生了虚拟IP故障转移"
            
        - alert: "HighFailoverRate"
          expr: 'rate(virtual_ip_failover_total[5m]) > 0.1'
          for: "2m"
          labels:
            severity: "critical"
          annotations:
            summary: "虚拟IP故障转移频率过高"
            description: "虚拟IP故障转移频率过高，可能存在网络问题"

# DaemonSet配置 (在每个节点上运行路由管理)
daemonSet:
  enabled: false  # 默认禁用，根据需要启用
  updateStrategy:
    type: "RollingUpdate"
    rollingUpdate:
      maxUnavailable: 1
  nodeSelector:
    kubernetes.io/os: "linux"
  tolerations:
    - key: "node-role.kubernetes.io/master"
      operator: "Exists"
      effect: "NoSchedule"
