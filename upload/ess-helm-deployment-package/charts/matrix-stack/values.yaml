# ESS-HELM 主配置文件
# 版本: 25.6.2 (官方稳定版)
# 模式: 支持外部服务器和内部服务器部署
# 功能: Router WAN IP检测 + 虚拟公网IP路由 + 增强管理

# 全局配置
global:
  serverName: "${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"
  domain: "${MAIN_DOMAIN}"
  
  # 镜像配置
  imageRegistry: ""
  imagePullSecrets: []
  
  # 存储配置
  storageClass: ""
  
  # 网络配置
  networkPolicy:
    enabled: true
    
# Element Web配置
elementWeb:
  enabled: true
  
  # 镜像配置
  image:
    repository: "vectorim/element-web"
    tag: "v1.11.50"
    pullPolicy: "IfNotPresent"
    
  # 服务配置
  service:
    type: "ClusterIP"
    port: 80
    
  # Ingress配置
  ingress:
    enabled: true
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    hosts:
      - host: "${ELEMENT_SUBDOMAIN}.${MAIN_DOMAIN}"
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: "element-web-tls"
        hosts:
          - "${ELEMENT_SUBDOMAIN}.${MAIN_DOMAIN}"
          
  # 配置
  config:
    default_server_config:
      m.homeserver:
        base_url: "https://${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}:${HTTPS_PORT}"
        server_name: "${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"
      m.identity_server:
        base_url: "https://vector.im"
        
  # 资源配置
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "200m"

# Synapse配置
synapse:
  enabled: true
  
  # 镜像配置
  image:
    repository: "matrixdotorg/synapse"
    tag: "v1.97.0"
    pullPolicy: "IfNotPresent"
    
  # 服务器配置
  serverName: "${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"
  publicBaseurl: "https://${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}:${HTTPS_PORT}"
  
  # 服务配置
  service:
    type: "ClusterIP"
    port: 8008
    
  # Ingress配置
  ingress:
    enabled: true
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    hosts:
      - host: "${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: "synapse-tls"
        hosts:
          - "${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"
  
  # 联邦配置
  federation:
    enabled: true
    port: 8448
    
  # Workers配置
  workers:
    enabled: false
    
  # 配置
  config:
    # 基本配置
    server_name: "${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"
    public_baseurl: "https://${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}:${HTTPS_PORT}"
    
    # 数据库配置
    database:
      name: "psycopg2"
      args:
        user: "synapse"
        password: "${POSTGRES_PASSWORD}"
        database: "synapse"
        host: "postgresql"
        port: 5432
        cp_min: 5
        cp_max: 10
        
    # 日志配置
    log_config: "/data/log.config"
    
    # 媒体配置
    media_store_path: "/data/media"
    max_upload_size: "50M"
    
    # 注册配置
    enable_registration: false
    registration_requires_token: true
    
    # TURN配置 (本地化)
    turn_uris:
      - "turn:${TURN_SUBDOMAIN}.${MAIN_DOMAIN}:3478?transport=udp"
      - "turn:${TURN_SUBDOMAIN}.${MAIN_DOMAIN}:3478?transport=tcp"
      - "turns:${TURN_SUBDOMAIN}.${MAIN_DOMAIN}:5349?transport=tcp"
    turn_shared_secret: "${TURN_SHARED_SECRET}"
    turn_user_lifetime: "1h"
    turn_allow_guests: true
    
  # 资源配置
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "2Gi"
      cpu: "1000m"
      
  # 存储配置
  persistence:
    enabled: true
    size: "20Gi"
    accessMode: "ReadWriteOnce"

# Matrix认证服务配置
matrixAuthenticationService:
  enabled: true
  
  # 镜像配置
  image:
    repository: "ghcr.io/element-hq/matrix-authentication-service"
    tag: "v0.5.0"
    pullPolicy: "IfNotPresent"
    
  # 服务配置
  service:
    type: "ClusterIP"
    port: 8080
    
  # Ingress配置
  ingress:
    enabled: true
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    hosts:
      - host: "${MAS_SUBDOMAIN}.${MAIN_DOMAIN}"
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: "mas-tls"
        hosts:
          - "${MAS_SUBDOMAIN}.${MAIN_DOMAIN}"
          
  # 资源配置
  resources:
    requests:
      memory: "256Mi"
      cpu: "200m"
    limits:
      memory: "512Mi"
      cpu: "400m"

# Matrix RTC配置
matrixRtc:
  enabled: true
  
  # 镜像配置
  image:
    repository: "ghcr.io/element-hq/matrix-rtc"
    tag: "v0.3.0"
    pullPolicy: "IfNotPresent"
    
  # 服务配置
  service:
    type: "ClusterIP"
    port: 8090
    
  # Ingress配置
  ingress:
    enabled: true
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    hosts:
      - host: "${RTC_SUBDOMAIN}.${MAIN_DOMAIN}"
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: "rtc-tls"
        hosts:
          - "${RTC_SUBDOMAIN}.${MAIN_DOMAIN}"
          
  # 资源配置
  resources:
    requests:
      memory: "256Mi"
      cpu: "200m"
    limits:
      memory: "512Mi"
      cpu: "400m"

# HAProxy负载均衡器配置
haproxy:
  enabled: true
  
  # 镜像配置
  image:
    repository: "haproxy"
    tag: "2.8-alpine"
    pullPolicy: "IfNotPresent"
    
  # 服务配置
  service:
    type: "ClusterIP"
    port: 80
    
  # 配置
  config: |
    global
        daemon
        log stdout local0
        
    defaults
        mode http
        timeout connect 5000ms
        timeout client 50000ms
        timeout server 50000ms
        
    frontend matrix_frontend
        bind *:80
        redirect scheme https if !{ ssl_fc }
        
    frontend matrix_frontend_ssl
        bind *:443 ssl crt /etc/ssl/certs/
        default_backend synapse_backend
        
    backend synapse_backend
        balance roundrobin
        server synapse synapse:8008 check
        
  # 资源配置
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "200m"

# PostgreSQL数据库配置
postgresql:
  enabled: true
  
  # 镜像配置
  image:
    repository: "postgres"
    tag: "15-alpine"
    pullPolicy: "IfNotPresent"
    
  # 认证配置
  auth:
    postgresPassword: "${POSTGRES_PASSWORD}"
    username: "synapse"
    password: "${POSTGRES_PASSWORD}"
    database: "synapse"
    
  # 主节点配置
  primary:
    persistence:
      enabled: true
      size: "10Gi"
      accessMode: "ReadWriteOnce"
      
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"
        
    # PostgreSQL配置
    configuration: |
      max_connections = 100
      shared_buffers = 128MB
      effective_cache_size = 512MB
      maintenance_work_mem = 64MB
      checkpoint_completion_target = 0.9
      wal_buffers = 16MB
      default_statistics_target = 100
      random_page_cost = 1.1
      effective_io_concurrency = 200

# Redis配置 (可选)
redis:
  enabled: false
  
  # 镜像配置
  image:
    repository: "redis"
    tag: "7-alpine"
    pullPolicy: "IfNotPresent"
    
  # 认证配置
  auth:
    enabled: false
    
  # 主节点配置
  master:
    persistence:
      enabled: true
      size: "1Gi"
      accessMode: "ReadWriteOnce"
      
    resources:
      requests:
        memory: "64Mi"
        cpu: "50m"
      limits:
        memory: "128Mi"
        cpu: "100m"

# SSL/TLS配置
tls:
  mode: "${SSL_MODE}"
  
  # Let's Encrypt配置
  letsencrypt:
    enabled: false
    email: ""
    server: "https://acme-v02.api.letsencrypt.org/directory"
    
  # 自定义证书配置
  custom:
    enabled: false
    certFile: ""
    keyFile: ""
    
  # 自签名证书配置
  selfsigned:
    enabled: true
    
# 监控配置
monitoring:
  enabled: true
  
  # Prometheus配置
  prometheus:
    enabled: false
    
  # Grafana配置
  grafana:
    enabled: false
    
  # 日志配置
  logging:
    level: "INFO"
    
# 备份配置
backup:
  enabled: true
  schedule: "0 2 * * *"
  retention: "7d"
  
# 网络策略
networkPolicy:
  enabled: true
  
# 服务账户
serviceAccount:
  create: true
  name: ""
  annotations: {}

# Pod安全策略
podSecurityPolicy:
  enabled: false
  
# 安全上下文
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000

# 节点选择器
nodeSelector: {}

# 容忍度
tolerations: []

# 亲和性
affinity: {}
