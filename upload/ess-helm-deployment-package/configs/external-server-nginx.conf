# ESS-HELM 外部服务器 Nginx 反向代理配置
# 版本: v2.0
# 功能: 反向代理到内部Matrix服务器，实现指路牌功能
# 适用: 外部服务器环境，公网访问，支持Router WAN IP检测

# 全局配置
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 事件配置
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# HTTP配置
http {
    # 基本配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 50M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 上游服务器配置
    upstream synapse_backend {
        server synapse.matrix.svc.cluster.local:8008;
        keepalive 32;
    }
    
    upstream element_backend {
        server element-web.matrix.svc.cluster.local:80;
        keepalive 32;
    }
    
    upstream mas_backend {
        server matrix-authentication-service.matrix.svc.cluster.local:8080;
        keepalive 32;
    }
    
    upstream rtc_backend {
        server matrix-rtc.matrix.svc.cluster.local:8090;
        keepalive 32;
    }
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    
    # Element Web服务器配置
    server {
        listen 80;
        listen [::]:80;
        server_name ${ELEMENT_SUBDOMAIN}.${MAIN_DOMAIN};
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name ${ELEMENT_SUBDOMAIN}.${MAIN_DOMAIN};
        
        # SSL配置
        ssl_certificate /etc/ssl/certs/element-web.crt;
        ssl_certificate_key /etc/ssl/private/element-web.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # 根目录
        location / {
            proxy_pass http://element_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 缓存配置
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }
    }
    
    # Matrix服务器配置
    server {
        listen 80;
        listen [::]:80;
        server_name ${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN};
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        listen 8448 ssl http2;  # 联邦端口
        listen [::]:8448 ssl http2;
        server_name ${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN};
        
        # SSL配置
        ssl_certificate /etc/ssl/certs/matrix.crt;
        ssl_certificate_key /etc/ssl/private/matrix.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # 客户端API
        location ~ ^(/_matrix|/_synapse/client) {
            proxy_pass http://synapse_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 限流
            limit_req zone=api burst=20 nodelay;
            
            # 超时配置
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # 联邦API
        location ~ ^/_matrix/federation {
            proxy_pass http://synapse_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 管理API
        location ~ ^/_synapse/admin {
            proxy_pass http://synapse_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 限制访问
            allow ***********/16;
            allow 10.0.0.0/8;
            allow **********/12;
            deny all;
        }
        
        # 登录限流
        location ~ ^/_matrix/client/(r0|v3)/login {
            proxy_pass http://synapse_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            limit_req zone=login burst=5 nodelay;
        }
        
        # 媒体上传
        location ~ ^/_matrix/media {
            proxy_pass http://synapse_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 增加超时时间
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # 增加缓冲区大小
            proxy_buffering off;
            proxy_request_buffering off;
        }
        
        # Well-known配置
        location /.well-known/matrix/server {
            return 200 '{"m.server": "${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}:8448"}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
        }
        
        location /.well-known/matrix/client {
            return 200 '{"m.homeserver":{"base_url":"https://${MATRIX_SUBDOMAIN}.${MAIN_DOMAIN}"},"m.identity_server":{"base_url":"https://vector.im"}}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
        }
    }
    
    # MAS认证服务配置
    server {
        listen 80;
        listen [::]:80;
        server_name ${MAS_SUBDOMAIN}.${MAIN_DOMAIN};
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name ${MAS_SUBDOMAIN}.${MAIN_DOMAIN};
        
        # SSL配置
        ssl_certificate /etc/ssl/certs/mas.crt;
        ssl_certificate_key /etc/ssl/private/mas.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        location / {
            proxy_pass http://mas_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    # RTC服务配置
    server {
        listen 80;
        listen [::]:80;
        server_name ${RTC_SUBDOMAIN}.${MAIN_DOMAIN};
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name ${RTC_SUBDOMAIN}.${MAIN_DOMAIN};
        
        # SSL配置
        ssl_certificate /etc/ssl/certs/rtc.crt;
        ssl_certificate_key /etc/ssl/private/rtc.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        location / {
            proxy_pass http://rtc_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
    
    # 健康检查端点
    server {
        listen 8080;
        server_name localhost;
        
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow ***********/16;
            allow 10.0.0.0/8;
            allow **********/12;
            deny all;
        }
    }
}

# Stream配置 (TURN服务)
stream {
    # 日志配置
    error_log /var/log/nginx/stream_error.log warn;
    
    # TURN UDP代理
    upstream turn_udp_backend {
        server turn.matrix.svc.cluster.local:3478;
    }
    
    server {
        listen 3478 udp;
        proxy_pass turn_udp_backend;
        proxy_timeout 1s;
        proxy_responses 1;
        proxy_bind $remote_addr transparent;
    }
    
    # TURN TCP代理
    upstream turn_tcp_backend {
        server turn.matrix.svc.cluster.local:3478;
    }
    
    server {
        listen 3478;
        proxy_pass turn_tcp_backend;
        proxy_timeout 10s;
        proxy_connect_timeout 5s;
    }
    
    # TURN TLS代理
    upstream turn_tls_backend {
        server turn.matrix.svc.cluster.local:5349;
    }
    
    server {
        listen 5349;
        proxy_pass turn_tls_backend;
        proxy_timeout 10s;
        proxy_connect_timeout 5s;
    }
}
