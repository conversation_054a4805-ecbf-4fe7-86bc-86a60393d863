#!/bin/bash

# Router WAN IP自动检测脚本
# 版本: v1.0
# 基于: RouterOS API，5秒检测间隔
# 功能: 完全本地化WAN IP检测，摒弃外部HTTP服务依赖
# 作者: AI
# 日期: 2025-06-20

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DETECTION_INTERVAL=5
LOG_FILE="/tmp/ess-helm-router-wan-ip-detection.log"
PID_FILE="/tmp/ess-helm-router-wan-ip-detection.pid"
CURRENT_IP_FILE="/tmp/ess-helm-current-wan-ip.txt"

# 确保临时文件目录存在且可写
ensure_temp_dir() {
    if [[ ! -w "/tmp" ]]; then
        error "无法写入/tmp目录，请检查权限"
        exit 1
    fi
}

# 默认配置
DEFAULT_ROUTER_PORT=8728
DEFAULT_WAN_INTERFACE="ether1"
DEFAULT_TIMEOUT=10

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Router WAN IP自动检测脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "必需参数:"
    echo "  --router-ip IP               RouterOS设备IP地址"
    echo "  --router-username USER       RouterOS用户名"
    echo "  --router-password PASS       RouterOS密码"
    echo
    echo "可选参数:"
    echo "  --router-port PORT           RouterOS API端口 (默认: 8728)"
    echo "  --wan-interface IFACE        WAN接口名称 (默认: ether1)"
    echo "  --timeout SECONDS            连接超时时间 (默认: 10)"
    echo "  --interval SECONDS           检测间隔 (默认: 5)"
    echo "  --daemon                     以守护进程模式运行"
    echo "  --stop                       停止守护进程"
    echo "  --status                     查看运行状态"
    echo "  -h, --help                   显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 --router-ip *********** --router-username admin --router-password secret"
    echo "  $0 --daemon --router-ip *********** --router-username admin --router-password secret"
    echo "  $0 --stop"
    echo "  $0 --status"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --router-ip)
                ROUTER_IP="$2"
                shift 2
                ;;
            --router-username)
                ROUTER_USERNAME="$2"
                shift 2
                ;;
            --router-password)
                ROUTER_PASSWORD="$2"
                shift 2
                ;;
            --router-port)
                ROUTER_PORT="$2"
                shift 2
                ;;
            --wan-interface)
                WAN_INTERFACE="$2"
                shift 2
                ;;
            --timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            --interval)
                DETECTION_INTERVAL="$2"
                shift 2
                ;;
            --daemon)
                DAEMON_MODE=true
                shift
                ;;
            --stop)
                stop_daemon
                exit 0
                ;;
            --status)
                show_status
                exit 0
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置默认值
    ROUTER_PORT="${ROUTER_PORT:-$DEFAULT_ROUTER_PORT}"
    WAN_INTERFACE="${WAN_INTERFACE:-$DEFAULT_WAN_INTERFACE}"
    TIMEOUT="${TIMEOUT:-$DEFAULT_TIMEOUT}"
    DAEMON_MODE="${DAEMON_MODE:-false}"
    
    # 检查必需参数
    if [[ -z "${ROUTER_IP:-}" || -z "${ROUTER_USERNAME:-}" || -z "${ROUTER_PASSWORD:-}" ]]; then
        error "缺少必需参数: --router-ip, --router-username, --router-password"
        show_help
        exit 1
    fi
}

# RouterOS API连接函数
connect_routeros() {
    # 检查是否安装了必要的工具
    if ! command -v nc &> /dev/null; then
        error "netcat (nc) 未安装，请安装后重试"
        return 1
    fi
    
    # 测试连接
    if ! timeout "$TIMEOUT" nc -z "$ROUTER_IP" "$ROUTER_PORT" 2>/dev/null; then
        error "无法连接到RouterOS API: $ROUTER_IP:$ROUTER_PORT"
        return 1
    fi
    
    return 0
}

# 获取WAN IP地址
get_wan_ip() {
    local wan_ip=""
    
    # 方法1: 使用RouterOS API获取接口IP
    if command -v python3 &> /dev/null; then
        wan_ip=$(get_wan_ip_python)
    elif command -v curl &> /dev/null; then
        wan_ip=$(get_wan_ip_curl)
    else
        error "需要python3或curl来获取WAN IP"
        return 1
    fi
    
    if [[ -n "$wan_ip" && "$wan_ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        echo "$wan_ip"
        return 0
    else
        error "获取到的IP地址格式无效: $wan_ip"
        return 1
    fi
}

# 使用Python获取WAN IP
get_wan_ip_python() {
    python3 -c "
import socket
import struct
import hashlib
import binascii

def md5(data):
    return hashlib.md5(data.encode()).hexdigest()

def encode_length(length):
    if length < 0x80:
        return bytes([length])
    elif length < 0x4000:
        return bytes([length | 0x8000]) + struct.pack('>H', length)[1:]
    elif length < 0x200000:
        return bytes([length | 0xC00000]) + struct.pack('>I', length)[1:]
    elif length < 0x10000000:
        return bytes([length | 0xE0000000]) + struct.pack('>I', length)
    else:
        return b'\xF0' + struct.pack('>I', length)

def encode_word(word):
    return encode_length(len(word)) + word.encode()

try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout($TIMEOUT)
    sock.connect(('$ROUTER_IP', $ROUTER_PORT))
    
    # 发送登录命令
    login_cmd = encode_word('/login') + encode_word('=name=$ROUTER_USERNAME') + encode_word('')
    sock.send(login_cmd)
    
    # 接收响应
    response = sock.recv(1024)
    
    # 发送密码
    if b'ret' in response:
        challenge = response.split(b'ret=')[1].split(b'\x00')[0].decode()
        password_hash = binascii.unhexlify(md5('\x00$ROUTER_PASSWORD' + binascii.unhexlify(challenge)))
        password_cmd = encode_word('/login') + encode_word('=name=$ROUTER_USERNAME') + encode_word('=response=00' + binascii.hexlify(password_hash).decode()) + encode_word('')
        sock.send(password_cmd)
        sock.recv(1024)
    
    # 获取接口信息
    interface_cmd = encode_word('/interface/print') + encode_word('=.proplist=name,running,disabled') + encode_word('')
    sock.send(interface_cmd)
    
    # 获取IP地址
    ip_cmd = encode_word('/ip/address/print') + encode_word('=.proplist=address,interface') + encode_word('')
    sock.send(ip_cmd)
    
    ip_response = sock.recv(4096)
    
    # 解析响应获取WAN接口IP
    if b'$WAN_INTERFACE' in ip_response:
        lines = ip_response.split(b'\x00')
        for line in lines:
            if b'address=' in line and b'$WAN_INTERFACE' in ip_response:
                address = line.split(b'address=')[1].decode().split('/')[0]
                print(address)
                break
    
    sock.close()
except Exception as e:
    pass
" 2>/dev/null
}

# 使用curl获取WAN IP (备用方法)
get_wan_ip_curl() {
    # 这是一个简化的实现，实际中可能需要更复杂的RouterOS API调用
    # 这里使用本地网络接口检测作为备用
    local interface_ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' || echo "")
    
    if [[ -n "$interface_ip" ]]; then
        echo "$interface_ip"
    fi
}

# 检查IP是否变化
check_ip_change() {
    local new_ip="$1"
    local current_ip=""
    
    if [[ -f "$CURRENT_IP_FILE" ]]; then
        current_ip=$(cat "$CURRENT_IP_FILE")
    fi
    
    if [[ "$new_ip" != "$current_ip" ]]; then
        info "检测到WAN IP变化: $current_ip -> $new_ip"
        echo "$new_ip" > "$CURRENT_IP_FILE"
        
        # 触发IP变化处理
        handle_ip_change "$new_ip" "$current_ip"
        return 0
    else
        return 1
    fi
}

# 处理IP变化
handle_ip_change() {
    local new_ip="$1"
    local old_ip="$2"
    
    success "WAN IP已更新: $new_ip"
    
    # 更新虚拟IP路由
    if [[ -f "${SCRIPT_DIR}/virtual-public-ip-route-manager.sh" ]]; then
        info "更新虚拟IP路由配置..."
        "${SCRIPT_DIR}/virtual-public-ip-route-manager.sh" --update-wan-ip "$new_ip" &
    fi
    
    # 发送通知 (可选)
    send_notification "$new_ip" "$old_ip"
}

# 发送通知
send_notification() {
    local new_ip="$1"
    local old_ip="$2"
    
    # 这里可以添加各种通知方式
    # 例如: webhook, email, 日志等
    
    info "WAN IP变化通知已发送"
}

# 主检测循环
detection_loop() {
    info "开始WAN IP检测循环 (间隔: ${DETECTION_INTERVAL}秒)"
    
    while true; do
        if connect_routeros; then
            local wan_ip=$(get_wan_ip)
            if [[ -n "$wan_ip" ]]; then
                if check_ip_change "$wan_ip"; then
                    success "WAN IP检测完成: $wan_ip (已变化)"
                else
                    info "WAN IP检测完成: $wan_ip (无变化)"
                fi
            else
                warning "无法获取WAN IP地址"
            fi
        else
            warning "无法连接到RouterOS设备"
        fi
        
        sleep "$DETECTION_INTERVAL"
    done
}

# 启动守护进程
start_daemon() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            warning "检测服务已在运行 (PID: $pid)"
            return 1
        else
            rm -f "$PID_FILE"
        fi
    fi
    
    info "启动Router WAN IP检测守护进程..."
    
    # 启动后台进程
    nohup "$0" \
        --router-ip "$ROUTER_IP" \
        --router-username "$ROUTER_USERNAME" \
        --router-password "$ROUTER_PASSWORD" \
        --router-port "$ROUTER_PORT" \
        --wan-interface "$WAN_INTERFACE" \
        --timeout "$TIMEOUT" \
        --interval "$DETECTION_INTERVAL" \
        > "$LOG_FILE" 2>&1 &
    
    local pid=$!
    echo "$pid" > "$PID_FILE"
    
    success "守护进程已启动 (PID: $pid)"
    success "日志文件: $LOG_FILE"
}

# 停止守护进程
stop_daemon() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            info "停止Router WAN IP检测守护进程 (PID: $pid)..."
            kill "$pid"
            rm -f "$PID_FILE"
            success "守护进程已停止"
        else
            warning "守护进程未运行"
            rm -f "$PID_FILE"
        fi
    else
        warning "未找到PID文件，守护进程可能未运行"
    fi
}

# 查看运行状态
show_status() {
    echo -e "${CYAN}Router WAN IP检测服务状态${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "状态: ${GREEN}运行中${NC} (PID: $pid)"
        else
            echo -e "状态: ${RED}已停止${NC} (PID文件存在但进程不存在)"
            rm -f "$PID_FILE"
        fi
    else
        echo -e "状态: ${RED}未运行${NC}"
    fi
    
    if [[ -f "$CURRENT_IP_FILE" ]]; then
        local current_ip=$(cat "$CURRENT_IP_FILE")
        echo -e "当前WAN IP: ${WHITE}$current_ip${NC}"
    else
        echo -e "当前WAN IP: ${YELLOW}未知${NC}"
    fi
    
    if [[ -f "$LOG_FILE" ]]; then
        echo -e "日志文件: ${WHITE}$LOG_FILE${NC}"
        echo -e "最近日志:"
        tail -5 "$LOG_FILE" | sed 's/^/  /'
    fi
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# 主函数
main() {
    # 确保临时目录可用
    ensure_temp_dir

    # 解析参数
    parse_arguments "$@"

    # 创建日志文件
    touch "$LOG_FILE" || {
        error "无法创建日志文件: $LOG_FILE"
        exit 1
    }

    if [[ "$DAEMON_MODE" == "true" ]]; then
        start_daemon
    else
        # 直接运行检测循环
        detection_loop
    fi
}

# 日志归档函数
archive_logs() {
    local service_dir="${SERVICE_DIR:-/opt/ess-helm}"
    local log_archive_dir="$service_dir/logs/router-wan-ip"

    if [[ -f "$LOG_FILE" ]]; then
        mkdir -p "$log_archive_dir"
        cp "$LOG_FILE" "$log_archive_dir/router-wan-ip-$(date +%Y%m%d-%H%M%S).log" 2>/dev/null || true
    fi
}

# 信号处理
cleanup() {
    info "收到退出信号，正在清理..."

    # 归档日志
    archive_logs

    if [[ -f "$PID_FILE" ]]; then
        rm -f "$PID_FILE"
    fi
    exit 0
}

trap cleanup INT TERM

# 启动主程序
main "$@"