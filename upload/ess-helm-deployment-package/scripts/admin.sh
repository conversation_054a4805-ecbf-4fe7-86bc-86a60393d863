#!/bin/bash

# ess 管理脚本
# 版本: v1.0
# 基于: ESS-HELM 25.6.2 官方稳定版
# 功能: 用户管理 (基于MAS CLI) + 服务控制 + 注册控制 + 运维管理
# 作者: AI
# 日期: 2025-06-20

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
NAMESPACE="matrix"
RELEASE_NAME="matrix-stack"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# MAS服务配置
MAS_SERVICE_AVAILABLE=false

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ESS-HELM 增强管理系统                      ║"
    echo "║                                                              ║"
    echo "║  🧑‍💼 用户管理 | 🔧 服务控制 | 📝 注册控制 | 🛠️ 运维管理        ║"
    echo "║                                                              ║"
    echo "║  基于 Matrix Authentication Service + Kubernetes 原生管理     ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# 初始化MAS服务配置
init_mas_config() {
    # 检查Matrix Authentication Service是否可用
    local mas_deployment=$(kubectl get deployment -n "$NAMESPACE" -l app.kubernetes.io/name=matrix-authentication-service -o name 2>/dev/null | head -1)

    if [[ -n "$mas_deployment" ]]; then
        MAS_SERVICE_AVAILABLE=true
        info "已找到Matrix Authentication Service: $(echo $mas_deployment | cut -d'/' -f2)"
        return 0
    else
        warning "无法找到Matrix Authentication Service"
        info "可能的原因："
        info "  • ESS-HELM尚未部署到Kubernetes集群"
        info "  • Matrix Authentication Service尚未启动"
        info "  • 命名空间 '$NAMESPACE' 不存在或无权限访问"
        info ""
        info "可用功能："
        info "  • 服务控制 (查看状态、重启服务等)"
        info "  • 运维管理 (资源监控、日志查看等)"
        info "  • 系统状态查看"
        info ""
        info "不可用功能："
        info "  • 用户管理 (需要Matrix Authentication Service)"
        info "  • 注册控制 (需要Matrix Authentication Service)"
        MAS_SERVICE_AVAILABLE=false
        return 1
    fi
}



# 检查MAS服务是否可用
check_mas_service_available() {
    if [[ "$MAS_SERVICE_AVAILABLE" != "true" ]]; then
        error "Matrix Authentication Service不可用，此功能需要已部署的ESS-HELM系统"
        echo -e "${YELLOW}提示: 请先使用部署脚本部署ESS-HELM系统${NC}"
        echo -e "${YELLOW}按任意键返回主菜单...${NC}"
        read -r
        return 1
    fi
    return 0
}

# MAS CLI调用
call_mas_cli() {
    local command="$1"
    shift
    local args=("$@")

    # 检查MAS服务是否可用
    if ! check_mas_service_available; then
        return 1
    fi

    # 执行mas-cli命令
    kubectl exec -n "$NAMESPACE" -it deploy/ess-matrix-authentication-service -- \
        mas-cli "$command" "${args[@]}"
}

# 主菜单
show_main_menu() {
    while true; do
        clear
        show_welcome
        echo -e "${WHITE}请选择管理功能:${NC}"
        echo
        echo -e "${GREEN}1.${NC} 用户管理 (创建/删除/修改用户)"
        echo -e "${GREEN}2.${NC} 服务控制 (启动/停止/重启/扩缩容)"
        echo -e "${GREEN}3.${NC} 注册控制 (开关注册/令牌管理)"
        echo -e "${GREEN}4.${NC} 运维管理 (备份/日志/监控)"
        echo -e "${GREEN}5.${NC} 系统状态 (查看部署状态)"
        echo -e "${GREEN}6.${NC} 配置管理 (查看/修改配置)"
        echo -e "${GREEN}0.${NC} 退出程序"
        echo
        echo -e "${YELLOW}请选择 (0-6):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                user_management_menu
                ;;
            2)
                service_control_menu
                ;;
            3)
                registration_control_menu
                ;;
            4)
                operations_management_menu
                ;;
            5)
                show_system_status
                ;;
            6)
                configuration_management_menu
                ;;
            0)
                echo -e "${GREEN}感谢使用 ESS-HELM 增强管理系统！${NC}"
                exit 0
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 用户管理菜单
user_management_menu() {
    # 检查MAS服务是否可用
    if ! check_mas_service_available; then
        return
    fi

    while true; do
        clear
        echo -e "${CYAN}用户管理${NC}"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo
        echo -e "${GREEN}1.${NC} 创建用户"
        echo -e "${GREEN}2.${NC} 查看用户列表"
        echo -e "${GREEN}3.${NC} 重置用户密码"
        echo -e "${GREEN}4.${NC} 设置管理员权限 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}5.${NC} 停用用户 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}6.${NC} 查看用户详情 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}0.${NC} 返回主菜单"
        echo
        echo -e "${YELLOW}请选择 (0-6):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                create_user
                ;;
            2)
                list_users
                ;;
            3)
                reset_user_password
                ;;
            4)
                show_function_not_implemented "设置管理员权限"
                ;;
            5)
                show_function_not_implemented "停用用户"
                ;;
            6)
                show_function_not_implemented "查看用户详情"
                ;;
            0)
                return
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 显示功能未实现提示
show_function_not_implemented() {
    local function_name="$1"
    echo -e "${CYAN}$function_name${NC}"
    echo
    warning "此功能正在开发中，敬请期待"
    echo -e "${YELLOW}按任意键返回...${NC}"
    read -r
}

# 创建用户
create_user() {
    echo -e "${CYAN}创建新用户${NC}"
    echo

    echo -e "${YELLOW}请输入用户名 (不包含@和域名):${NC}"
    read -r username

    echo -e "${YELLOW}请输入用户密码:${NC}"
    read -r -s password
    echo

    echo -e "${YELLOW}请输入显示名称 (可选):${NC}"
    read -r display_name

    echo -e "${YELLOW}请输入邮箱地址 (可选):${NC}"
    read -r email

    info "正在创建用户: $username"

    # 使用MAS CLI创建用户 (交互式)
    echo -e "${YELLOW}注意: 接下来将进入交互式用户创建流程${NC}"
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r

    if call_mas_cli "manage" "register-user"; then
        success "用户创建流程已启动"
        info "请按照提示完成用户创建"
    else
        error "用户创建失败"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 查看用户列表
list_users() {
    echo -e "${CYAN}用户列表${NC}"
    echo

    info "正在获取用户列表..."

    # 注意: MAS CLI可能没有直接的list-users命令，这里使用kubectl查看MAS相关的用户信息
    echo -e "${YELLOW}注意: 此功能将显示MAS服务的用户管理界面${NC}"
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r

    if call_mas_cli "manage" "--help"; then
        info "MAS CLI管理命令帮助已显示"
        echo -e "${YELLOW}请使用相应的MAS CLI命令查看用户信息${NC}"
    else
        error "无法访问MAS CLI"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 重置用户密码
reset_user_password() {
    echo -e "${CYAN}重置用户密码${NC}"
    echo

    warning "此功能需要通过MAS CLI的用户管理界面完成"
    info "MAS (Matrix Authentication Service) 使用不同的用户管理模式"
    echo

    echo -e "${YELLOW}建议操作步骤:${NC}"
    echo "1. 使用 kubectl exec 进入 MAS 容器"
    echo "2. 运行 mas-cli manage 命令"
    echo "3. 按照交互式界面重置用户密码"
    echo

    echo -e "${YELLOW}是否要进入MAS管理界面? (y/n):${NC}"
    read -r choice

    if [[ "$choice" =~ ^[Yy]$ ]]; then
        info "正在启动MAS管理界面..."
        call_mas_cli "manage"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 服务控制菜单
service_control_menu() {
    while true; do
        clear
        echo -e "${CYAN}服务控制${NC}"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo
        echo -e "${GREEN}1.${NC} 查看服务状态"
        echo -e "${GREEN}2.${NC} 重启服务"
        echo -e "${GREEN}3.${NC} 停止服务"
        echo -e "${GREEN}4.${NC} 启动服务"
        echo -e "${GREEN}5.${NC} 扩缩容服务"
        echo -e "${GREEN}6.${NC} 查看服务日志"
        echo -e "${GREEN}0.${NC} 返回主菜单"
        echo
        echo -e "${YELLOW}请选择 (0-6):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                show_service_status
                ;;
            2)
                restart_service
                ;;
            3)
                stop_service
                ;;
            4)
                start_service
                ;;
            5)
                scale_service
                ;;
            6)
                show_service_logs
                ;;
            0)
                return
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 查看服务状态
show_service_status() {
    echo -e "${CYAN}服务状态${NC}"
    echo
    
    info "正在获取服务状态..."
    
    echo -e "${WHITE}Pods状态:${NC}"
    kubectl get pods -n "$NAMESPACE" -o wide
    echo
    
    echo -e "${WHITE}Services状态:${NC}"
    kubectl get services -n "$NAMESPACE"
    echo
    
    echo -e "${WHITE}Deployments状态:${NC}"
    kubectl get deployments -n "$NAMESPACE"
    echo
    
    echo -e "${WHITE}StatefulSets状态:${NC}"
    kubectl get statefulsets -n "$NAMESPACE"
    echo
    
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 重启服务
restart_service() {
    echo -e "${CYAN}重启服务${NC}"
    echo
    
    echo -e "${YELLOW}请选择要重启的服务:${NC}"
    echo "1. Synapse"
    echo "2. Element Web"
    echo "3. Matrix Authentication Service"
    echo "4. Matrix RTC"
    echo "5. HAProxy"
    echo "6. PostgreSQL"
    echo "0. 返回"
    
    read -r service_choice
    
    local service_name=""
    case "$service_choice" in
        1)
            service_name="synapse"
            ;;
        2)
            service_name="element-web"
            ;;
        3)
            service_name="matrix-authentication-service"
            ;;
        4)
            service_name="matrix-rtc"
            ;;
        5)
            service_name="haproxy"
            ;;
        6)
            service_name="postgresql"
            ;;
        0)
            return
            ;;
        *)
            error "无效选择"
            return
            ;;
    esac
    
    info "正在重启服务: $service_name"
    
    # 查找对应的deployment或statefulset
    local deployment=$(kubectl get deployment -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null | head -1)
    local statefulset=$(kubectl get statefulset -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null | head -1)
    
    if [[ -n "$deployment" ]]; then
        kubectl rollout restart "$deployment" -n "$NAMESPACE"
        kubectl rollout status "$deployment" -n "$NAMESPACE"
        success "服务重启完成: $service_name"
    elif [[ -n "$statefulset" ]]; then
        kubectl rollout restart "$statefulset" -n "$NAMESPACE"
        kubectl rollout status "$statefulset" -n "$NAMESPACE"
        success "服务重启完成: $service_name"
    else
        error "未找到服务: $service_name"
    fi
    
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 停止服务
stop_service() {
    echo -e "${CYAN}停止服务${NC}"
    echo

    echo -e "${YELLOW}请选择要停止的服务:${NC}"
    echo "1. Synapse"
    echo "2. Element Web"
    echo "3. Matrix Authentication Service"
    echo "4. Matrix RTC"
    echo "5. HAProxy"
    echo "6. PostgreSQL"
    echo "0. 返回"

    read -r service_choice

    local service_name=""
    case "$service_choice" in
        1) service_name="synapse" ;;
        2) service_name="element-web" ;;
        3) service_name="matrix-authentication-service" ;;
        4) service_name="matrix-rtc" ;;
        5) service_name="haproxy" ;;
        6) service_name="postgresql" ;;
        0) return ;;
        *) error "无效选择"; return ;;
    esac

    info "正在停止服务: $service_name"

    local deployment=$(kubectl get deployment -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null | head -1)
    local statefulset=$(kubectl get statefulset -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null | head -1)

    if [[ -n "$deployment" ]]; then
        kubectl scale "$deployment" --replicas=0 -n "$NAMESPACE"
        success "服务已停止: $service_name"
    elif [[ -n "$statefulset" ]]; then
        kubectl scale "$statefulset" --replicas=0 -n "$NAMESPACE"
        success "服务已停止: $service_name"
    else
        error "未找到服务: $service_name"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 启动服务
start_service() {
    echo -e "${CYAN}启动服务${NC}"
    echo

    echo -e "${YELLOW}请选择要启动的服务:${NC}"
    echo "1. Synapse"
    echo "2. Element Web"
    echo "3. Matrix Authentication Service"
    echo "4. Matrix RTC"
    echo "5. HAProxy"
    echo "6. PostgreSQL"
    echo "0. 返回"

    read -r service_choice

    local service_name=""
    case "$service_choice" in
        1) service_name="synapse" ;;
        2) service_name="element-web" ;;
        3) service_name="matrix-authentication-service" ;;
        4) service_name="matrix-rtc" ;;
        5) service_name="haproxy" ;;
        6) service_name="postgresql" ;;
        0) return ;;
        *) error "无效选择"; return ;;
    esac

    info "正在启动服务: $service_name"

    local deployment=$(kubectl get deployment -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null | head -1)
    local statefulset=$(kubectl get statefulset -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null | head -1)

    if [[ -n "$deployment" ]]; then
        kubectl scale "$deployment" --replicas=1 -n "$NAMESPACE"
        kubectl rollout status "$deployment" -n "$NAMESPACE"
        success "服务已启动: $service_name"
    elif [[ -n "$statefulset" ]]; then
        kubectl scale "$statefulset" --replicas=1 -n "$NAMESPACE"
        kubectl rollout status "$statefulset" -n "$NAMESPACE"
        success "服务已启动: $service_name"
    else
        error "未找到服务: $service_name"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 扩缩容服务
scale_service() {
    echo -e "${CYAN}扩缩容服务${NC}"
    echo

    echo -e "${YELLOW}请选择要扩缩容的服务:${NC}"
    echo "1. Synapse"
    echo "2. Element Web"
    echo "3. Matrix Authentication Service"
    echo "4. Matrix RTC"
    echo "5. HAProxy"
    echo "0. 返回"

    read -r service_choice

    local service_name=""
    case "$service_choice" in
        1) service_name="synapse" ;;
        2) service_name="element-web" ;;
        3) service_name="matrix-authentication-service" ;;
        4) service_name="matrix-rtc" ;;
        5) service_name="haproxy" ;;
        0) return ;;
        *) error "无效选择"; return ;;
    esac

    echo -e "${YELLOW}请输入副本数量:${NC}"
    read -r replicas

    if ! [[ "$replicas" =~ ^[0-9]+$ ]]; then
        error "无效的副本数量"
        return
    fi

    info "正在设置服务副本数: $service_name -> $replicas"

    local deployment=$(kubectl get deployment -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null | head -1)
    local statefulset=$(kubectl get statefulset -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null | head -1)

    if [[ -n "$deployment" ]]; then
        kubectl scale "$deployment" --replicas="$replicas" -n "$NAMESPACE"
        kubectl rollout status "$deployment" -n "$NAMESPACE"
        success "服务扩缩容完成: $service_name -> $replicas 副本"
    elif [[ -n "$statefulset" ]]; then
        kubectl scale "$statefulset" --replicas="$replicas" -n "$NAMESPACE"
        kubectl rollout status "$statefulset" -n "$NAMESPACE"
        success "服务扩缩容完成: $service_name -> $replicas 副本"
    else
        error "未找到服务: $service_name"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 查看服务日志
show_service_logs() {
    echo -e "${CYAN}查看服务日志${NC}"
    echo

    echo -e "${YELLOW}请选择要查看日志的服务:${NC}"
    echo "1. Synapse"
    echo "2. Element Web"
    echo "3. Matrix Authentication Service"
    echo "4. Matrix RTC"
    echo "5. HAProxy"
    echo "6. PostgreSQL"
    echo "0. 返回"

    read -r service_choice

    local service_name=""
    case "$service_choice" in
        1) service_name="synapse" ;;
        2) service_name="element-web" ;;
        3) service_name="matrix-authentication-service" ;;
        4) service_name="matrix-rtc" ;;
        5) service_name="haproxy" ;;
        6) service_name="postgresql" ;;
        0) return ;;
        *) error "无效选择"; return ;;
    esac

    echo -e "${YELLOW}请输入要显示的日志行数 (默认: 100):${NC}"
    read -r lines
    lines=${lines:-100}

    info "正在获取服务日志: $service_name (最近 $lines 行)"

    local pods=$(kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name="$service_name" -o name 2>/dev/null)

    if [[ -n "$pods" ]]; then
        for pod in $pods; do
            echo -e "${WHITE}=== $(echo $pod | cut -d'/' -f2) ===${NC}"
            kubectl logs "$pod" -n "$NAMESPACE" --tail="$lines"
            echo
        done
    else
        error "未找到服务的Pod: $service_name"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 注册控制菜单
registration_control_menu() {
    # 检查MAS服务是否可用
    if ! check_mas_service_available; then
        return
    fi

    while true; do
        clear
        echo -e "${CYAN}注册控制${NC}"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo
        echo -e "${GREEN}1.${NC} 查看注册状态 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}2.${NC} 开启/关闭注册 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}3.${NC} 创建注册令牌"
        echo -e "${GREEN}4.${NC} 查看注册令牌列表 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}5.${NC} 删除注册令牌 ${YELLOW}(功能开发中)${NC}"
        echo -e "${GREEN}0.${NC} 返回主菜单"
        echo
        echo -e "${YELLOW}请选择 (0-5):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                show_function_not_implemented "查看注册状态"
                ;;
            2)
                show_function_not_implemented "开启/关闭注册"
                ;;
            3)
                create_registration_token
                ;;
            4)
                show_function_not_implemented "查看注册令牌列表"
                ;;
            5)
                show_function_not_implemented "删除注册令牌"
                ;;
            0)
                return
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 创建注册令牌
create_registration_token() {
    echo -e "${CYAN}创建注册令牌${NC}"
    echo

    warning "注册令牌管理需要通过MAS CLI完成"
    info "Matrix Authentication Service 使用不同的注册管理模式"
    echo

    echo -e "${YELLOW}建议操作步骤:${NC}"
    echo "1. 使用 kubectl exec 进入 MAS 容器"
    echo "2. 运行 mas-cli manage 命令"
    echo "3. 按照交互式界面管理注册令牌"
    echo

    echo -e "${YELLOW}是否要进入MAS管理界面? (y/n):${NC}"
    read -r choice

    if [[ "$choice" =~ ^[Yy]$ ]]; then
        info "正在启动MAS管理界面..."
        call_mas_cli "manage"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 运维管理菜单
operations_management_menu() {
    while true; do
        clear
        echo -e "${CYAN}运维管理${NC}"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo
        echo -e "${GREEN}1.${NC} 查看系统资源使用情况"
        echo -e "${GREEN}2.${NC} 备份数据库"
        echo -e "${GREEN}3.${NC} 恢复数据库"
        echo -e "${GREEN}4.${NC} 查看详细日志"
        echo -e "${GREEN}5.${NC} 清理旧日志"
        echo -e "${GREEN}6.${NC} 系统健康检查"
        echo -e "${GREEN}0.${NC} 返回主菜单"
        echo
        echo -e "${YELLOW}请选择 (0-6):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                show_resource_usage
                ;;
            2)
                backup_database
                ;;
            3)
                restore_database
                ;;
            4)
                show_detailed_logs
                ;;
            5)
                cleanup_old_logs
                ;;
            6)
                system_health_check
                ;;
            0)
                return
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 查看系统资源使用情况
show_resource_usage() {
    echo -e "${CYAN}系统资源使用情况${NC}"
    echo
    
    info "正在获取资源使用情况..."
    
    echo -e "${WHITE}节点资源使用:${NC}"
    kubectl top nodes 2>/dev/null || echo "需要安装metrics-server"
    echo
    
    echo -e "${WHITE}Pod资源使用:${NC}"
    kubectl top pods -n "$NAMESPACE" 2>/dev/null || echo "需要安装metrics-server"
    echo
    
    echo -e "${WHITE}存储使用情况:${NC}"
    kubectl get pvc -n "$NAMESPACE"
    echo
    
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 备份数据库
backup_database() {
    echo -e "${CYAN}备份数据库${NC}"
    echo

    local backup_dir="/tmp/ess-helm-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"

    info "正在备份数据库到: $backup_dir"

    # 获取PostgreSQL Pod
    local postgres_pod=$(kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=postgresql -o name 2>/dev/null | head -1)

    if [[ -n "$postgres_pod" ]]; then
        local pod_name=$(echo "$postgres_pod" | cut -d'/' -f2)
        info "找到PostgreSQL Pod: $pod_name"

        # 执行数据库备份
        kubectl exec -n "$NAMESPACE" "$pod_name" -- pg_dumpall -U postgres > "$backup_dir/database-backup.sql"

        if [[ $? -eq 0 ]]; then
            success "数据库备份完成: $backup_dir/database-backup.sql"
            info "备份文件大小: $(du -h "$backup_dir/database-backup.sql" | cut -f1)"
        else
            error "数据库备份失败"
        fi
    else
        error "未找到PostgreSQL服务"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 恢复数据库
restore_database() {
    echo -e "${CYAN}恢复数据库${NC}"
    echo

    warning "数据库恢复是危险操作，将覆盖现有数据！"
    echo -e "${YELLOW}请输入备份文件路径:${NC}"
    read -r backup_file

    if [[ ! -f "$backup_file" ]]; then
        error "备份文件不存在: $backup_file"
        return
    fi

    echo -e "${RED}确认要恢复数据库吗？这将覆盖所有现有数据！(yes/no):${NC}"
    read -r confirm

    if [[ "$confirm" != "yes" ]]; then
        info "数据库恢复已取消"
        return
    fi

    local postgres_pod=$(kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=postgresql -o name 2>/dev/null | head -1)

    if [[ -n "$postgres_pod" ]]; then
        local pod_name=$(echo "$postgres_pod" | cut -d'/' -f2)
        info "正在恢复数据库..."

        kubectl exec -i -n "$NAMESPACE" "$pod_name" -- psql -U postgres < "$backup_file"

        if [[ $? -eq 0 ]]; then
            success "数据库恢复完成"
        else
            error "数据库恢复失败"
        fi
    else
        error "未找到PostgreSQL服务"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 查看详细日志
show_detailed_logs() {
    echo -e "${CYAN}查看详细日志${NC}"
    echo

    echo -e "${YELLOW}请选择日志类型:${NC}"
    echo "1. 所有服务日志"
    echo "2. 错误日志"
    echo "3. 最近事件"
    echo "4. 资源事件"
    echo "0. 返回"

    read -r log_choice

    case "$log_choice" in
        1)
            info "获取所有服务日志..."
            kubectl logs -n "$NAMESPACE" --all-containers=true --tail=200
            ;;
        2)
            info "获取错误日志..."
            kubectl get events -n "$NAMESPACE" --field-selector type=Warning
            ;;
        3)
            info "获取最近事件..."
            kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp'
            ;;
        4)
            info "获取资源事件..."
            kubectl describe pods -n "$NAMESPACE"
            ;;
        0)
            return
            ;;
        *)
            error "无效选择"
            ;;
    esac

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 清理旧日志
cleanup_old_logs() {
    echo -e "${CYAN}清理旧日志${NC}"
    echo

    warning "此操作将清理系统中的旧日志文件"
    echo -e "${YELLOW}确认要清理旧日志吗? (y/n):${NC}"
    read -r confirm

    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        info "正在清理旧日志..."

        # 清理临时日志文件
        find /tmp -name "ess-helm-*.log" -mtime +7 -delete 2>/dev/null || true

        # 清理Kubernetes日志
        kubectl delete events -n "$NAMESPACE" --field-selector type=Normal 2>/dev/null || true

        success "旧日志清理完成"
    else
        info "日志清理已取消"
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 配置管理菜单
configuration_management_menu() {
    while true; do
        clear
        echo -e "${CYAN}配置管理${NC}"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo
        echo -e "${GREEN}1.${NC} 查看配置文件"
        echo -e "${GREEN}2.${NC} 查看ConfigMaps"
        echo -e "${GREEN}3.${NC} 查看Secrets"
        echo -e "${GREEN}4.${NC} 编辑配置 ${YELLOW}(谨慎操作)${NC}"
        echo -e "${GREEN}0.${NC} 返回主菜单"
        echo
        echo -e "${YELLOW}请选择 (0-4):${NC}"
        read -r choice

        case "$choice" in
            1)
                show_config_files
                ;;
            2)
                show_configmaps
                ;;
            3)
                show_secrets
                ;;
            4)
                edit_configuration
                ;;
            0)
                return
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 查看配置文件
show_config_files() {
    echo -e "${CYAN}配置文件${NC}"
    echo

    info "正在获取配置文件..."

    echo -e "${WHITE}ConfigMaps:${NC}"
    kubectl get configmaps -n "$NAMESPACE"
    echo

    echo -e "${WHITE}Secrets:${NC}"
    kubectl get secrets -n "$NAMESPACE"
    echo

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 查看ConfigMaps
show_configmaps() {
    echo -e "${CYAN}ConfigMaps详情${NC}"
    echo

    local configmaps=$(kubectl get configmaps -n "$NAMESPACE" -o name 2>/dev/null)

    for cm in $configmaps; do
        local cm_name=$(echo "$cm" | cut -d'/' -f2)
        echo -e "${WHITE}=== $cm_name ===${NC}"
        kubectl describe configmap "$cm_name" -n "$NAMESPACE"
        echo
    done

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 查看Secrets
show_secrets() {
    echo -e "${CYAN}Secrets详情${NC}"
    echo

    warning "Secrets包含敏感信息，请谨慎查看"
    echo -e "${YELLOW}确认要查看Secrets吗? (y/n):${NC}"
    read -r confirm

    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        local secrets=$(kubectl get secrets -n "$NAMESPACE" -o name 2>/dev/null)

        for secret in $secrets; do
            local secret_name=$(echo "$secret" | cut -d'/' -f2)
            echo -e "${WHITE}=== $secret_name ===${NC}"
            kubectl describe secret "$secret_name" -n "$NAMESPACE"
            echo
        done
    fi

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 编辑配置
edit_configuration() {
    echo -e "${CYAN}编辑配置${NC}"
    echo

    warning "编辑配置可能影响系统稳定性，请谨慎操作！"
    echo -e "${YELLOW}请选择要编辑的资源类型:${NC}"
    echo "1. ConfigMap"
    echo "2. Secret"
    echo "3. Deployment"
    echo "0. 返回"

    read -r resource_choice

    case "$resource_choice" in
        1)
            echo -e "${YELLOW}请输入ConfigMap名称:${NC}"
            read -r cm_name
            kubectl edit configmap "$cm_name" -n "$NAMESPACE"
            ;;
        2)
            echo -e "${YELLOW}请输入Secret名称:${NC}"
            read -r secret_name
            kubectl edit secret "$secret_name" -n "$NAMESPACE"
            ;;
        3)
            echo -e "${YELLOW}请输入Deployment名称:${NC}"
            read -r deploy_name
            kubectl edit deployment "$deploy_name" -n "$NAMESPACE"
            ;;
        0)
            return
            ;;
        *)
            error "无效选择"
            ;;
    esac

    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 系统健康检查
system_health_check() {
    echo -e "${CYAN}系统健康检查${NC}"
    echo
    
    info "正在执行系统健康检查..."
    
    # 检查Pod状态
    echo -e "${WHITE}检查Pod健康状态:${NC}"
    local unhealthy_pods=$(kubectl get pods -n "$NAMESPACE" --field-selector=status.phase!=Running -o name 2>/dev/null | wc -l)
    if [[ "$unhealthy_pods" -eq 0 ]]; then
        success "所有Pod运行正常"
    else
        warning "发现 $unhealthy_pods 个异常Pod"
        kubectl get pods -n "$NAMESPACE" --field-selector=status.phase!=Running
    fi
    echo
    
    # 检查服务连通性
    echo -e "${WHITE}检查服务连通性:${NC}"
    local services=$(kubectl get service -n "$NAMESPACE" -o name)
    for service in $services; do
        local service_name=$(echo "$service" | cut -d'/' -f2)
        echo -n "检查 $service_name: "
        if kubectl get endpoints -n "$NAMESPACE" "$service_name" -o jsonpath='{.subsets[*].addresses[*].ip}' | grep -q .; then
            echo -e "${GREEN}正常${NC}"
        else
            echo -e "${RED}异常${NC}"
        fi
    done
    echo
    
    success "健康检查完成"
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -r
}

# 显示系统状态
show_system_status() {
    if [[ "$1" == "--status" ]]; then
        # 命令行模式，直接显示状态
        show_service_status
        return
    fi
    
    # 交互模式
    show_service_status
}

# 主程序入口
main() {
    # 检查kubectl可用性
    if ! command -v kubectl &> /dev/null; then
        error "kubectl未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &>/dev/null; then
        error "无法连接到Kubernetes集群，请检查kubeconfig配置"
        exit 1
    fi
    
    # 初始化MAS服务配置（允许失败）
    init_mas_config || true

    # 处理命令行参数
    if [[ $# -gt 0 && "$1" == "--status" ]]; then
        show_system_status --status
        exit 0
    fi

    # 显示主菜单
    show_main_menu
}

# 信号处理
trap 'error "脚本被中断"; exit 1' INT TERM

# 启动主程序
main "$@"