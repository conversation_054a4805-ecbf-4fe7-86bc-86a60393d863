#!/bin/bash

# 外部服务器部署脚本 (Nginx反向代理)
# 版本: v2.1
# 基于: ESS-HELM 25.6.2 官方稳定版
# 功能: 部署Nginx反向代理服务，支持Router WAN IP检测
# 作者: AI
# 日期: 2025-06-20

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ESS_HELM_VERSION="25.6.2"
NGINX_CONTAINER_NAME="ess-helm-nginx-proxy"
NGINX_IMAGE="nginx:1.25-alpine"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 默认配置
DEFAULT_HTTPS_PORT="443"
DEFAULT_HTTP_PORT="80"
DEFAULT_FEDERATION_PORT="8448"
DEFAULT_SERVICE_DIR="$HOME/matrix-proxy"
DEFAULT_SSL_MODE="letsencrypt"
DEFAULT_INTERNAL_SERVER_IP=""

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "外部服务器部署脚本 (Nginx反向代理)"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "必需参数:"
    echo "  --domain DOMAIN              主域名 (例如: example.com)"
    echo "  --internal-server IP         内部Matrix服务器IP地址"
    echo
    echo "可选参数:"
    echo "  --element-subdomain SUB      Element Web子域名 (默认: element)"
    echo "  --matrix-subdomain SUB       Matrix服务器子域名 (默认: matrix)"
    echo "  --mas-subdomain SUB          MAS认证服务子域名 (默认: mas)"
    echo "  --rtc-subdomain SUB          RTC服务子域名 (默认: rtc)"
    echo "  --turn-subdomain SUB         TURN服务子域名 (默认: turn)"
    echo "  --https-port PORT            HTTPS端口 (默认: 443)"
    echo "  --http-port PORT             HTTP端口 (默认: 80)"
    echo "  --federation-port PORT       Matrix联邦端口 (默认: 8448)"
    echo "  --service-dir DIR            服务目录 (默认: ~/matrix-proxy)"
    echo "  --ssl-mode MODE              SSL模式 (letsencrypt|custom)"
    echo "  --ssl-cert PATH              自定义SSL证书路径"
    echo "  --ssl-key PATH               自定义SSL私钥路径"
    echo "  --router-ip IP               RouterOS IP地址"
    echo "  --router-username USER       RouterOS用户名"
    echo "  --router-password PASS       RouterOS密码"
    echo "  --wan-interface IFACE        WAN接口名称 (默认: ether1)"
    echo "  -h, --help                   显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 --domain example.com --internal-server *************"
    echo "  $0 --domain example.com --internal-server ************* --router-ip ***********"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --domain)
                MAIN_DOMAIN="$2"
                shift 2
                ;;
            --internal-server)
                INTERNAL_SERVER_IP="$2"
                shift 2
                ;;
            --element-subdomain)
                ELEMENT_SUBDOMAIN="$2"
                shift 2
                ;;
            --matrix-subdomain)
                MATRIX_SUBDOMAIN="$2"
                shift 2
                ;;
            --mas-subdomain)
                MAS_SUBDOMAIN="$2"
                shift 2
                ;;
            --rtc-subdomain)
                RTC_SUBDOMAIN="$2"
                shift 2
                ;;
            --turn-subdomain)
                TURN_SUBDOMAIN="$2"
                shift 2
                ;;
            --https-port)
                HTTPS_PORT="$2"
                shift 2
                ;;
            --http-port)
                HTTP_PORT="$2"
                shift 2
                ;;
            --federation-port)
                FEDERATION_PORT="$2"
                shift 2
                ;;
            --service-dir)
                SERVICE_DIR="$2"
                shift 2
                ;;
            --ssl-mode)
                SSL_MODE="$2"
                shift 2
                ;;
            --ssl-cert)
                SSL_CERT_PATH="$2"
                shift 2
                ;;
            --ssl-key)
                SSL_KEY_PATH="$2"
                shift 2
                ;;
            --router-ip)
                ROUTER_IP="$2"
                shift 2
                ;;
            --router-username)
                ROUTER_USERNAME="$2"
                shift 2
                ;;
            --router-password)
                ROUTER_PASSWORD="$2"
                shift 2
                ;;
            --wan-interface)
                WAN_INTERFACE="$2"
                shift 2
                ;;
            --letsencrypt-email)
                LETSENCRYPT_EMAIL="$2"
                shift 2
                ;;
            --letsencrypt-env)
                LETSENCRYPT_ENV="$2"
                shift 2
                ;;
            --letsencrypt-consent)
                LETSENCRYPT_EMAIL_CONSENT="$2"
                shift 2
                ;;
            --cloudflare-token)
                CLOUDFLARE_API_TOKEN="$2"
                shift 2
                ;;
            --cloudflare-email)
                CLOUDFLARE_EMAIL="$2"
                shift 2
                ;;
            --cloudflare-zone)
                CLOUDFLARE_ZONE_ID="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 检查必需参数
    if [[ -z "${MAIN_DOMAIN:-}" ]]; then
        error "缺少必需参数: --domain"
        show_help
        exit 1
    fi

    if [[ -z "${INTERNAL_SERVER_IP:-}" ]]; then
        error "缺少必需参数: --internal-server"
        show_help
        exit 1
    fi

    # 设置默认值
    ELEMENT_SUBDOMAIN="${ELEMENT_SUBDOMAIN:-element}"
    MATRIX_SUBDOMAIN="${MATRIX_SUBDOMAIN:-matrix}"
    MAS_SUBDOMAIN="${MAS_SUBDOMAIN:-mas}"
    RTC_SUBDOMAIN="${RTC_SUBDOMAIN:-rtc}"
    TURN_SUBDOMAIN="${TURN_SUBDOMAIN:-turn}"
    HTTPS_PORT="${HTTPS_PORT:-$DEFAULT_HTTPS_PORT}"
    HTTP_PORT="${HTTP_PORT:-$DEFAULT_HTTP_PORT}"
    FEDERATION_PORT="${FEDERATION_PORT:-$DEFAULT_FEDERATION_PORT}"
    SERVICE_DIR="${SERVICE_DIR:-$DEFAULT_SERVICE_DIR}"
    SSL_MODE="${SSL_MODE:-$DEFAULT_SSL_MODE}"
    WAN_INTERFACE="${WAN_INTERFACE:-ether1}"

    # SSL配置参数默认值 (可选参数)
    LETSENCRYPT_EMAIL="${LETSENCRYPT_EMAIL:-}"
    LETSENCRYPT_ENV="${LETSENCRYPT_ENV:-production}"
    LETSENCRYPT_EMAIL_CONSENT="${LETSENCRYPT_EMAIL_CONSENT:-false}"
    CLOUDFLARE_API_TOKEN="${CLOUDFLARE_API_TOKEN:-}"
    CLOUDFLARE_EMAIL="${CLOUDFLARE_EMAIL:-}"
    CLOUDFLARE_ZONE_ID="${CLOUDFLARE_ZONE_ID:-}"
}

# 验证环境
validate_environment() {
    info "验证外部服务器环境..."

    # 检查Docker
    if ! docker version &>/dev/null; then
        error "Docker未安装或未启动"
        exit 1
    fi

    # 检查内部服务器连通性
    if ! ping -c 1 "$INTERNAL_SERVER_IP" &>/dev/null; then
        warning "无法ping通内部服务器 $INTERNAL_SERVER_IP，请确保网络连通性"
    fi

    # 检查端口占用
    if netstat -tuln | grep -q ":$HTTPS_PORT "; then
        error "端口 $HTTPS_PORT 已被占用"
        exit 1
    fi

    if netstat -tuln | grep -q ":$HTTP_PORT "; then
        error "端口 $HTTP_PORT 已被占用"
        exit 1
    fi

    success "环境验证通过"
}

# 创建服务目录
create_service_directory() {
    info "创建服务目录: $SERVICE_DIR"

    mkdir -p "$SERVICE_DIR"/{configs,ssl,logs}

    success "服务目录创建完成"
}

# 停止现有Nginx容器
stop_existing_nginx() {
    info "检查并停止现有Nginx容器..."

    if docker ps -q -f name="$NGINX_CONTAINER_NAME" | grep -q .; then
        info "停止现有Nginx容器..."
        docker stop "$NGINX_CONTAINER_NAME" || true
        docker rm "$NGINX_CONTAINER_NAME" || true
        success "现有Nginx容器已停止"
    fi
}

# 生成Nginx配置文件
generate_nginx_configuration() {
    info "生成Nginx反向代理配置文件..."

    local config_dir="$SERVICE_DIR/configs"
    mkdir -p "$config_dir"

    # 生成Nginx主配置文件
    generate_nginx_conf "$config_dir/nginx.conf"

    # 生成SSL配置
    if [[ "$SSL_MODE" == "letsencrypt" ]]; then
        setup_letsencrypt_ssl
    elif [[ "$SSL_MODE" == "custom" ]]; then
        setup_custom_ssl
    fi

    success "Nginx配置文件生成完成"
}

# 生成Nginx主配置文件
generate_nginx_conf() {
    local file_path="$1"

    cat > "$file_path" << EOF
# 外部服务器 Nginx 反向代理配置
# 版本: v2.0
# 功能: 反向代理到内部Matrix服务器
# 内部服务器: $INTERNAL_SERVER_IP

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                    '\$status \$body_bytes_sent "\$http_referer" '
                    '"\$http_user_agent" "\$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 50M;

    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 上游服务器配置 (指向内部Matrix服务器)
    upstream internal_matrix_server {
        server $INTERNAL_SERVER_IP:8443;
        keepalive 32;
    }

    # 限流配置
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=login:10m rate=1r/s;

    # Element Web服务器配置
    server {
        listen $HTTP_PORT;
        listen [::]:$HTTP_PORT;
        server_name $ELEMENT_SUBDOMAIN.$MAIN_DOMAIN;

        return 301 https://\$server_name\$request_uri;
    }

    server {
        listen $HTTPS_PORT ssl http2;
        listen [::]:$HTTPS_PORT ssl http2;
        server_name $ELEMENT_SUBDOMAIN.$MAIN_DOMAIN;

        ssl_certificate /etc/ssl/certs/nginx.crt;
        ssl_certificate_key /etc/ssl/private/nginx.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        location / {
            proxy_pass https://internal_matrix_server;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            proxy_ssl_verify off;
        }
    }

    # Matrix服务器配置
    server {
        listen $HTTP_PORT;
        listen [::]:$HTTP_PORT;
        server_name $MATRIX_SUBDOMAIN.$MAIN_DOMAIN;

        return 301 https://\$server_name\$request_uri;
    }

    server {
        listen $HTTPS_PORT ssl http2;
        listen [::]:$HTTPS_PORT ssl http2;
        listen $FEDERATION_PORT ssl http2;
        listen [::]:$FEDERATION_PORT ssl http2;
        server_name $MATRIX_SUBDOMAIN.$MAIN_DOMAIN;

        ssl_certificate /etc/ssl/certs/nginx.crt;
        ssl_certificate_key /etc/ssl/private/nginx.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        location / {
            proxy_pass https://internal_matrix_server;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            proxy_ssl_verify off;

            # 超时配置
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Well-known配置
        location /.well-known/matrix/server {
            return 200 '{"m.server": "$MATRIX_SUBDOMAIN.$MAIN_DOMAIN:$FEDERATION_PORT"}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
        }

        location /.well-known/matrix/client {
            return 200 '{"m.homeserver":{"base_url":"https://$MATRIX_SUBDOMAIN.$MAIN_DOMAIN"},"m.identity_server":{"base_url":"https://vector.im"}}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
        }
    }

EOF
}

# 设置SSL证书
setup_letsencrypt_ssl() {
    info "设置Let's Encrypt SSL证书..."

    # 检查certbot是否安装
    if ! command -v certbot &> /dev/null; then
        info "安装certbot..."
        if command -v apt-get &> /dev/null; then
            apt-get update && apt-get install -y certbot
        elif command -v yum &> /dev/null; then
            yum install -y certbot
        else
            error "无法安装certbot，请手动安装"
            exit 1
        fi
    fi

    # 确定使用的邮箱地址
    local cert_email="${LETSENCRYPT_EMAIL:-acme@$MAIN_DOMAIN}"

    # 生成证书
    certbot certonly --standalone \
        --email "$cert_email" \
        --agree-tos \
        --no-eff-email \
        -d "$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN" \
        -d "$MATRIX_SUBDOMAIN.$MAIN_DOMAIN" \
        -d "$MAS_SUBDOMAIN.$MAIN_DOMAIN" \
        -d "$RTC_SUBDOMAIN.$MAIN_DOMAIN" \
        -d "$TURN_SUBDOMAIN.$MAIN_DOMAIN"

    # 复制证书到服务目录
    cp "/etc/letsencrypt/live/$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN/fullchain.pem" "$SERVICE_DIR/ssl/nginx.crt"
    cp "/etc/letsencrypt/live/$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN/privkey.pem" "$SERVICE_DIR/ssl/nginx.key"

    success "Let's Encrypt SSL证书设置完成"
}

# 设置自定义SSL证书
setup_custom_ssl() {
    info "设置自定义SSL证书..."

    if [[ -z "${SSL_CERT_PATH:-}" ]] || [[ -z "${SSL_KEY_PATH:-}" ]]; then
        error "使用自定义SSL模式时必须提供证书和私钥路径"
        exit 1
    fi

    if [[ ! -f "$SSL_CERT_PATH" ]]; then
        error "SSL证书文件不存在: $SSL_CERT_PATH"
        exit 1
    fi

    if [[ ! -f "$SSL_KEY_PATH" ]]; then
        error "SSL私钥文件不存在: $SSL_KEY_PATH"
        exit 1
    fi

    # 复制证书到服务目录
    cp "$SSL_CERT_PATH" "$SERVICE_DIR/ssl/nginx.crt"
    cp "$SSL_KEY_PATH" "$SERVICE_DIR/ssl/nginx.key"

    success "自定义SSL证书设置完成"
}

# 启动Router WAN IP检测服务
start_router_wan_ip_detection() {
    if [[ -n "${ROUTER_IP:-}" ]]; then
        info "启动Router WAN IP检测服务..."

        # 复制检测脚本
        cp "$PROJECT_DIR/scripts/router-wan-ip-detector.sh" "$SERVICE_DIR/"
        chmod +x "$SERVICE_DIR/router-wan-ip-detector.sh"

        # 启动检测服务
        nohup "$SERVICE_DIR/router-wan-ip-detector.sh" \
            --router-ip "$ROUTER_IP" \
            --router-username "$ROUTER_USERNAME" \
            --router-password "$ROUTER_PASSWORD" \
            --wan-interface "$WAN_INTERFACE" \
            > "/tmp/ess-helm-router-wan-ip-detection.log" 2>&1 &

        echo $! > "/tmp/ess-helm-router-wan-ip-detection.pid"
        success "Router WAN IP检测服务已启动"
    else
        warning "未配置Router信息，跳过WAN IP检测服务"
    fi
}

# 部署Nginx反向代理
deploy_nginx_proxy() {
    info "部署Nginx反向代理容器..."

    # 停止现有容器
    stop_existing_nginx

    # 启动Nginx容器
    docker run -d \
        --name "$NGINX_CONTAINER_NAME" \
        --restart unless-stopped \
        -p "$HTTP_PORT:80" \
        -p "$HTTPS_PORT:443" \
        -p "$FEDERATION_PORT:8448" \
        -v "$SERVICE_DIR/configs/nginx.conf:/etc/nginx/nginx.conf:ro" \
        -v "$SERVICE_DIR/ssl:/etc/ssl:ro" \
        -v "$SERVICE_DIR/logs:/var/log/nginx" \
        "$NGINX_IMAGE"

    success "Nginx反向代理部署完成"
}

# 验证Nginx部署
verify_nginx_deployment() {
    info "验证Nginx部署状态..."

    # 检查容器状态
    if docker ps | grep -q "$NGINX_CONTAINER_NAME"; then
        success "Nginx容器运行正常"
        docker ps | grep "$NGINX_CONTAINER_NAME"
    else
        error "Nginx容器未运行"
        docker logs "$NGINX_CONTAINER_NAME" 2>/dev/null || true
        exit 1
    fi

    # 检查端口监听
    if netstat -tuln | grep -q ":$HTTPS_PORT "; then
        success "HTTPS端口 $HTTPS_PORT 监听正常"
    else
        warning "HTTPS端口 $HTTPS_PORT 未监听"
    fi

    if netstat -tuln | grep -q ":$HTTP_PORT "; then
        success "HTTP端口 $HTTP_PORT 监听正常"
    else
        warning "HTTP端口 $HTTP_PORT 未监听"
    fi
}

# 显示部署信息
show_deployment_info() {
    echo
    echo -e "${GREEN}Nginx反向代理部署完成！${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${WHITE}外部访问地址 (通过反向代理):${NC}"
    echo -e "Element Web: ${WHITE}https://$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "Matrix 服务器: ${WHITE}https://$MATRIX_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "MAS 认证服务: ${WHITE}https://$MAS_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "RTC 服务: ${WHITE}https://$RTC_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "TURN 服务: ${WHITE}$TURN_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo
    echo -e "${WHITE}内部Matrix服务器:${NC} ${CYAN}$INTERNAL_SERVER_IP:8443${NC}"
    echo
    echo -e "服务目录: ${WHITE}$SERVICE_DIR${NC}"
    echo -e "Nginx配置: ${WHITE}$SERVICE_DIR/configs/nginx.conf${NC}"
    echo -e "SSL证书: ${WHITE}$SERVICE_DIR/ssl/${NC}"
    echo -e "日志文件: ${WHITE}$SERVICE_DIR/logs/${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${YELLOW}管理命令:${NC}"
    echo -e "  查看容器状态: ${WHITE}docker ps | grep $NGINX_CONTAINER_NAME${NC}"
    echo -e "  查看Nginx日志: ${WHITE}docker logs $NGINX_CONTAINER_NAME${NC}"
    echo -e "  重启Nginx: ${WHITE}docker restart $NGINX_CONTAINER_NAME${NC}"
    echo -e "  停止服务: ${WHITE}docker stop $NGINX_CONTAINER_NAME${NC}"
    echo
}

# 复制日志文件到服务目录
copy_logs_to_service_dir() {
    local log_dir="$SERVICE_DIR/logs"
    mkdir -p "$log_dir"

    # 复制所有ESS-HELM相关的临时日志文件
    for log_file in /tmp/ess-helm-*.log /tmp/ess-helm-*.pid; do
        if [[ -f "$log_file" ]]; then
            cp "$log_file" "$log_dir/" 2>/dev/null || true
        fi
    done

    info "日志文件已复制到: $log_dir"
}

# 清理旧的临时文件
cleanup_old_temp_files() {
    # 清理超过7天的ESS-HELM临时文件
    find /tmp -name "ess-helm-*" -type f -mtime +7 -delete 2>/dev/null || true
}

# 主函数
main() {
    echo -e "${CYAN}外部服务器部署脚本 (Nginx反向代理)${NC}"
    echo -e "${CYAN}版本: v2.0 基于 ESS-HELM $ESS_HELM_VERSION${NC}"
    echo

    # 解析参数
    parse_arguments "$@"

    # 验证环境
    validate_environment

    # 创建服务目录
    create_service_directory

    # 生成Nginx配置文件
    generate_nginx_configuration

    # 启动Router WAN IP检测服务
    start_router_wan_ip_detection

    # 部署Nginx反向代理
    deploy_nginx_proxy

    # 验证部署
    verify_nginx_deployment

    # 复制日志文件到服务目录
    copy_logs_to_service_dir

    # 显示部署信息
    show_deployment_info
}

# 错误处理
trap 'error "部署过程中发生错误"; exit 1' ERR

# 启动主程序
main "$@"