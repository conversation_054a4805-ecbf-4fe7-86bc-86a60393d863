#!/bin/bash

# ESS-HELM 参数传递测试脚本
# 版本: v2.1
# 功能: 测试setup.sh向internal.sh和external.sh的参数传递
# 作者: AI
# 日期: 2025-06-20

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 日志函数
info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_parameter() {
    local script_name="$1"
    local parameter="$2"
    local description="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "测试 $script_name 参数 $parameter... "
    
    # 测试参数是否被识别 (不会报错"未知参数")
    if "$SCRIPT_DIR/$script_name" --help 2>&1 | grep -q "$parameter" || \
       "$SCRIPT_DIR/$script_name" "$parameter" "test-value" --help 2>&1 | grep -q "显示此帮助信息"; then
        echo -e "${GREEN}✓${NC} $description"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}✗${NC} $description"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 测试internal.sh参数支持
test_internal_parameters() {
    info "测试internal.sh参数支持..."
    
    # 基础参数测试
    test_parameter "internal.sh" "--domain" "域名参数"
    test_parameter "internal.sh" "--element-subdomain" "Element子域名参数"
    test_parameter "internal.sh" "--matrix-subdomain" "Matrix子域名参数"
    test_parameter "internal.sh" "--ssl-mode" "SSL模式参数"
    test_parameter "internal.sh" "--ssl-cert" "SSL证书参数"
    test_parameter "internal.sh" "--ssl-key" "SSL私钥参数"
    
    # 新增SSL配置参数测试
    test_parameter "internal.sh" "--letsencrypt-email" "Let's Encrypt邮箱参数"
    test_parameter "internal.sh" "--letsencrypt-env" "Let's Encrypt环境参数"
    test_parameter "internal.sh" "--letsencrypt-consent" "Let's Encrypt同意参数"
    test_parameter "internal.sh" "--cloudflare-token" "Cloudflare Token参数"
    test_parameter "internal.sh" "--cloudflare-email" "Cloudflare邮箱参数"
    test_parameter "internal.sh" "--cloudflare-zone" "Cloudflare Zone参数"
}

# 测试external.sh参数支持
test_external_parameters() {
    info "测试external.sh参数支持..."
    
    # 基础参数测试
    test_parameter "external.sh" "--domain" "域名参数"
    test_parameter "external.sh" "--internal-server" "内部服务器参数"
    test_parameter "external.sh" "--element-subdomain" "Element子域名参数"
    test_parameter "external.sh" "--ssl-mode" "SSL模式参数"
    test_parameter "external.sh" "--ssl-cert" "SSL证书参数"
    test_parameter "external.sh" "--ssl-key" "SSL私钥参数"
    
    # 新增SSL配置参数测试
    test_parameter "external.sh" "--letsencrypt-email" "Let's Encrypt邮箱参数"
    test_parameter "external.sh" "--letsencrypt-env" "Let's Encrypt环境参数"
    test_parameter "external.sh" "--letsencrypt-consent" "Let's Encrypt同意参数"
    test_parameter "external.sh" "--cloudflare-token" "Cloudflare Token参数"
    test_parameter "external.sh" "--cloudflare-email" "Cloudflare邮箱参数"
    test_parameter "external.sh" "--cloudflare-zone" "Cloudflare Zone参数"
}

# 测试参数传递一致性
test_parameter_consistency() {
    info "测试参数传递一致性..."
    
    # 检查setup.sh中传递的参数是否在子脚本中都有对应支持
    local setup_params=(
        "--letsencrypt-email"
        "--letsencrypt-env" 
        "--letsencrypt-consent"
        "--cloudflare-token"
        "--cloudflare-email"
        "--cloudflare-zone"
    )
    
    for param in "${setup_params[@]}"; do
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        echo -n "检查参数传递一致性 $param... "
        
        # 检查setup.sh是否传递此参数
        if grep -q "$param" "$PROJECT_DIR/setup.sh"; then
            # 检查internal.sh和external.sh是否都支持此参数
            if grep -q "$param" "$SCRIPT_DIR/internal.sh" && grep -q "$param" "$SCRIPT_DIR/external.sh"; then
                echo -e "${GREEN}✓${NC} 参数传递一致"
                PASSED_TESTS=$((PASSED_TESTS + 1))
            else
                echo -e "${RED}✗${NC} 参数传递不一致"
                FAILED_TESTS=$((FAILED_TESTS + 1))
            fi
        else
            echo -e "${YELLOW}?${NC} setup.sh中未找到此参数"
        fi
    done
}

# 测试脚本语法
test_script_syntax() {
    info "测试脚本语法正确性..."
    
    local scripts=("setup.sh" "internal.sh" "external.sh")
    
    for script in "${scripts[@]}"; do
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        echo -n "检查 $script 语法... "
        
        if bash -n "$PROJECT_DIR/$script" 2>/dev/null || bash -n "$SCRIPT_DIR/$script" 2>/dev/null; then
            echo -e "${GREEN}✓${NC} 语法正确"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗${NC} 语法错误"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    done
}

# 模拟参数传递测试
test_simulated_parameter_passing() {
    info "模拟参数传递测试..."
    
    # 创建临时测试脚本
    local test_script="/tmp/test-internal-params.sh"
    
    cat > "$test_script" << 'EOF'
#!/bin/bash
# 模拟internal.sh参数接收测试
source_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [[ -f "$source_dir/../upload/ess-helm-deployment-package/scripts/internal.sh" ]]; then
    # 提取参数解析函数进行测试
    echo "测试参数解析功能..."
    exit 0
else
    echo "找不到internal.sh文件"
    exit 1
fi
EOF
    
    chmod +x "$test_script"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "模拟参数传递测试... "
    
    if "$test_script" &>/dev/null; then
        echo -e "${GREEN}✓${NC} 模拟测试通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗${NC} 模拟测试失败"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    rm -f "$test_script"
}

# 显示测试结果
show_test_results() {
    echo
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${CYAN}参数传递测试结果摘要${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "总测试项: ${WHITE}$TOTAL_TESTS${NC}"
    echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"
    echo -e "成功率: ${WHITE}$(( PASSED_TESTS * 100 / TOTAL_TESTS ))%${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "${GREEN}🎉 所有参数传递测试都通过了！脚本已准备就绪。${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  有 $FAILED_TESTS 个测试失败，请检查并修复后再进行部署。${NC}"
        return 1
    fi
}

# 主函数
main() {
    echo -e "${CYAN}ESS-HELM 参数传递测试工具 v2.1${NC}"
    echo -e "${CYAN}测试setup.sh向internal.sh和external.sh的参数传递${NC}"
    echo
    
    # 检查脚本文件是否存在
    if [[ ! -f "$SCRIPT_DIR/internal.sh" ]]; then
        error "找不到internal.sh文件"
        exit 1
    fi
    
    if [[ ! -f "$SCRIPT_DIR/external.sh" ]]; then
        error "找不到external.sh文件"
        exit 1
    fi
    
    if [[ ! -f "$PROJECT_DIR/setup.sh" ]]; then
        error "找不到setup.sh文件"
        exit 1
    fi
    
    # 执行测试
    test_script_syntax
    echo
    
    test_internal_parameters
    echo
    
    test_external_parameters
    echo
    
    test_parameter_consistency
    echo
    
    test_simulated_parameter_passing
    echo
    
    show_test_results
}

# 启动测试
main "$@"
