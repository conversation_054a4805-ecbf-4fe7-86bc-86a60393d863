# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: element-web-minimal.yaml element-web-checkov.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  annotations:
    checkov.io/skip1: CKV_K8S_11=We deliberately don't set CPU limits. Pod is BestEffort not Guaranteed
    checkov.io/skip2: CKV_K8S_43=No digests
    checkov.io/skip3: CKV2_K8S_6=No network policy yet
  ingress:
    host: element.ess.localhost
matrixAuthenticationService:
  enabled: false
matrixRTC:
  enabled: false
synapse:
  enabled: false
wellKnownDelegation:
  enabled: false
