# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

matrixAuthenticationService:
  replicas: 2
  extraEnv:
  - name: DEBUG_RENDERING
    value: "1"

  podSecurityContext:
    runAsGroup: 0

  ingress:
    tlsSecret: "{{ $.Release.Name }}-mas-web-tls"
    host: mas.{{ $.Values.serverName }}

  additional:
    000-pytest-admin:
      configSecret: "{{ $.Release.Name }}-pytest-admin"
      configSecretKey: "admin.yaml"

  syn2mas:
    extraEnv:
    - name: DEBUG_RENDERING
      value: "1"

postgres:
  podSecurityContext:
    runAsGroup: 0
