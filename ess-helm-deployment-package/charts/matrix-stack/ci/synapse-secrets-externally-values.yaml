# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: synapse-minimal.yaml synapse-postgres.yaml synapse-additional-secrets-externally.yaml synapse-postgres-secrets-externally.yaml synapse-secrets-externally.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  enabled: false
matrixAuthenticationService:
  enabled: false
matrixRTC:
  enabled: false
serverName: ess.localhost
synapse:
  additional:
    00-userconfig.yaml:
      configSecret: '{{ $.Release.Name }}-synapse-secrets'
      configSecretKey: 00-userconfig.yaml
  appservices:
    - secret: '{{ $.Release.Name }}-synapse-external'
      secretKey: bridge_registration.yaml
  ingress:
    host: synapse.ess.localhost
  macaroon:
    secret: '{{ $.Release.Name }}-synapse-external'
    secretKey: macaroon
  postgres:
    database: synapse
    host: ess-postgres
    password:
      secret: '{{ $.Release.Name }}-synapse-external'
      secretKey: postgresPassword
    user: synapse_user
  registrationSharedSecret:
    secret: '{{ $.Release.Name }}-synapse-external'
    secretKey: registrationSharedSecret
  signingKey:
    secret: '{{ $.Release.Name }}-synapse-external'
    secretKey: signingKey
wellKnownDelegation:
  enabled: false
