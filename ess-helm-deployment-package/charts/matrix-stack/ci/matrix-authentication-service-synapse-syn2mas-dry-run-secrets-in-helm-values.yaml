# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: matrix-authentication-service-minimal.yaml matrix-authentication-service-postgres.yaml matrix-authentication-service-postgres-secrets-in-helm.yaml  matrix-authentication-service-secrets-in-helm.yaml synapse-minimal.yaml synapse-postgres.yaml synapse-postgres-secrets-in-helm.yaml synapse-secrets-in-helm.yaml matrix-authentication-service-syn2mas-dryrun.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  enabled: false
matrixAuthenticationService:
  additional:
    password-scheme.yml:
      config: |
        passwords:
          schemes:
            - version: 1
              algorithm: bcrypt
            - version: 2
              algorithm: argon2id
  encryptionSecret:
    value: CHANGEME-ahohhohgiavee5Koh8ahwo
  ingress:
    host: mas.ess.localhost
  postgres:
    database: mas
    host: postgres
    password:
      value: CHANGEME-ooWo6jeidahhei3Hae0eer9U
    user: mas
  privateKeys:
    ecdsaPrime256v1:
      value: |
        -----BEGIN EC PRIVATE KEY-----
        MHcCAQEEIYjZ789034nLz+oXJyVWqgUdDmRlKxvTfHsBhFtGpOaAoGCCqGSM49
        AwEHoUQDQgAE6521bYjZ789034nLz+oXJyVWqgUdDmRlKxvTfHsBhFtGpOaAoGCCqGSM49
        AwEAAKBcZW5jb2duZXQwgggYMIINL6Ado018734nLz+oXJyVWqgUdDmRlKxvTfHsBhFtGpOaAoGCCqGSM49AwEH
        ------END EC PRIVATE KEY-----
    ecdsaSecp256k1:
      value: |
        -----BEGIN EC PRIVATE KEY-----
        MHcCAQEEZFQZ789034nLz+oXJyVWqgUdDmRlKxvTfHsBhFtGpOaAoGCCqGSM49AwEHoUQDQgAE6521bYjZ789034nLz+oXJyVWqgUdDmRlKxvTfHsBhFtGpOaAoGCCqGSM49
        ------END EC PRIVATE KEY-----
    ecdsaSecp384r1:
      value: |
        -----BEGIN EC PRIVATE KEY-----
         MHcCAQEEZFQZ789034nLz+oXJyVWqgUdDmRlKxvTfHsBhFtGpOaAoGCCqGSM49AwEHoUQDQgAE6521bYjZ789034nLz+oXJyVWqgUdDmRlKxvTfHsBhFtGpOaAoGCCqGSM49
         ------END EC PRIVATE KEY-----
    rsa:
      value: |
        -----BEGIN RSA PRIVATE KEY-----
        MIIEowIBAAKCAQEA6521bYjZ789034nLz+oXJyVWqgUdDmRlKxvTfHsBhFtGpOaAoGCCqGSM49AwEHoUQDQgAE6521bYjZ789034nLz+oXJyVWqgUdDmRlKxvTfHsBhFtGpOaAoGCCqGSM49
        ------END RSA PRIVATE KEY-----
  syn2mas:
    dryRun: true
    enabled: true
  synapseOIDCClientSecret:
    value: CHANGEME-eiv6wae8shooPhie4ief8ru2egahbah0
  synapseSharedSecret:
    value: CHANGEME-iaw8eeSef4zeefie8ii3akien9tiaYah
matrixRTC:
  enabled: false
serverName: ess.localhost
synapse:
  ingress:
    host: synapse.ess.localhost
  macaroon:
    value: CHANGEME-eek3Eigoh8ux8laeTingeej1
  postgres:
    database: synapse
    host: ess-postgres
    password:
      value: CHANGEME-ooWo6jeidahhei3Hae0eer9U
    user: synapse_user
  registrationSharedSecret:
    value: CHANGEME-ooWo6jeidahhei3Hae0eer9U
  signingKey:
    value: ed25519 0 bNQOzBUDszff7Ax81z6w0uZ1IPWoxYaazT7emaZEfpw
wellKnownDelegation:
  enabled: false
