# ESS-HELM 最小可用配置
# 基于官方element-hq/ess-helm 25.6.2
# 此配置文件包含部署ESS-HELM所需的最小配置参数
# 严格遵循values.schema.json规范

# =============================================================================
# 全局配置
# =============================================================================

# Matrix服务器域名 - 这是最重要的配置，影响用户ID和房间ID格式
# 部署后不能更改，请谨慎设置
serverName: "matrix.example.com"

# =============================================================================
# Synapse Matrix服务器配置
# =============================================================================
synapse:
  enabled: true

  # Ingress配置 - 必需
  ingress:
    host: "matrix.example.com"
    annotations:
      nginx.ingress.kubernetes.io/proxy-body-size: "50m"

  # 资源配置 - 适合小型部署
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "2Gi"
      cpu: "1000m"

# =============================================================================
# Element Web客户端配置
# =============================================================================
elementWeb:
  enabled: true

  # Ingress配置 - 必需
  ingress:
    host: "element.example.com"

  # Element Web特定配置 - 使用additional属性（仅支持字符串值）
  additional:
    default_theme: "light"
    brand: "Element"

  # 资源配置
  resources:
    requests:
      memory: "128Mi"
      cpu: "50m"
    limits:
      memory: "256Mi"
      cpu: "200m"

# =============================================================================
# Matrix认证服务配置
# =============================================================================
matrixAuthenticationService:
  enabled: true

  # Ingress配置 - 必需
  ingress:
    host: "auth.example.com"

  # 资源配置
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "500m"

# =============================================================================
# Matrix RTC实时通信配置
# =============================================================================
matrixRTC:
  enabled: true

  # Ingress配置 - 必需
  ingress:
    host: "rtc.example.com"

  # 资源配置
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "1Gi"
      cpu: "500m"

# =============================================================================
# PostgreSQL数据库配置
# =============================================================================
postgres:
  enabled: true

  # 资源配置
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "2Gi"
      cpu: "1000m"

# =============================================================================
# Ingress全局配置
# =============================================================================
ingress:
  className: "nginx"
  tlsEnabled: true

  # 全局注解
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"

  # 服务类型
  service:
    type: ClusterIP

# =============================================================================
# 证书管理配置（可选）
# =============================================================================
# 如果使用cert-manager自动管理证书，请取消注释并配置
# certManager:
#   clusterIssuer: "letsencrypt-prod"

# =============================================================================
# 注意事项
# =============================================================================
# 1. 请将 example.com 替换为您的实际域名
# 2. 确保DNS解析正确指向您的Kubernetes集群
# 3. 确保防火墙开放必要端口（80, 443, 8448）
# 4. 此配置严格遵循values.schema.json规范
# 5. 高级配置请参考官方文档或values.yaml示例
