{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}
{{- with .Values.elementWeb -}}
{{- if .enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "element-io.element-web.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-element-web-nginx
  namespace: {{ $.Release.Namespace }}
data:
  {{- include "element-io.element-web.nginx-configmap-data" (dict "root" $) | nindent 4 -}}
{{- end }}
{{- end }}
