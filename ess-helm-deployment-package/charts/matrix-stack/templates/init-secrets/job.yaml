{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.initSecrets -}}
{{- if and .enabled (include "element-io.init-secrets.generated-secrets" (dict "root" $)) -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ $.Release.Name }}-init-secrets
  namespace: {{ $.Release.Namespace }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-10"

{{- with .annotations }}
    {{- toYaml . | nindent 4 }}
{{- end }}
  labels:
    {{- include "element-io.init-secrets.labels" (dict "root" $ "context" .) | nindent 4 }}
spec:
  backoffLimit: 6
  completionMode: NonIndexed
  completions: 1
  manualSelector: false
  parallelism: 1
  podReplacementPolicy: TerminatingOrFailed
  template:
    metadata:
      annotations:
{{- with .annotations }}
        {{- toYaml . | nindent 8 }}
{{- end }}
      labels:
        {{- include "element-io.init-secrets.labels" (dict "root" $ "context" (dict "labels" .labels "withChartVersion" false)) | nindent 8 }}
    spec:
      restartPolicy: OnFailure
{{- include "element-io.ess-library.pods.commonSpec" (dict "root" $ "context" (dict "componentValues" . "instanceSuffix" "init-secrets" "deployment" false "usesMatrixTools" true "mountServiceAccountToken" true)) | nindent 6 }}
      containers:
      - name: init-secrets
{{- with $.Values.matrixTools.image -}}
{{- if .digest }}
        image: "{{ .registry }}/{{ .repository }}@{{ .digest }}"
        imagePullPolicy: {{ .pullPolicy | default "IfNotPresent" }}
{{- else }}
        image: "{{ .registry }}/{{ .repository }}:{{ .tag }}"
        imagePullPolicy: {{ .pullPolicy | default "Always" }}
{{- end }}
{{- end }}
{{- with .containersSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
{{- end }}
{{- with .resources }}
        resources:
          {{- toYaml . | nindent 10 }}
{{- end }}
        {{- include "element-io.ess-library.pods.env" (dict "root" $ "context" (dict "componentValues" . "componentName" "init-secrets")) | nindent 8 }}
        command:
        - "/matrix-tools"
        - "generate-secrets"
        - "-secrets"
        - {{ include "element-io.init-secrets.generated-secrets" (dict "root" $ "context" .) | fromYamlArray | join "," | quote }}
        - "-labels"
        - {{ include "element-io.init-secrets.labels" (dict "root" $ "context" (dict "labels" .labels "withChartVersion" false)) | trim  | replace ": " "="| replace "\n" "," | replace "\"" "" | quote }}
{{- end -}}
{{- end -}}
