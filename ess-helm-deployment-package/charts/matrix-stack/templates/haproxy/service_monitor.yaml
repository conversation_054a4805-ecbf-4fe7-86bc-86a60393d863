{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- if or $.Values.synapse.enabled $.Values.wellKnownDelegation.enabled -}}
{{- with .Values.haproxy -}}
{{- if $.Capabilities.APIVersions.Has "monitoring.coreos.com/v1/ServiceMonitor" }}
{{- if .serviceMonitors.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    {{- include "element-io.haproxy.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-haproxy
  namespace: {{ $.Release.Namespace }}
spec:
  endpoints:
  - interval: 30s
    port: haproxy-metrics
  selector:
    matchLabels:
      app.kubernetes.io/part-of: matrix-stack
      app.kubernetes.io/component: matrix-stack-ingress
      app.kubernetes.io/instance: {{ $.Release.Name }}-haproxy
{{- end }}
{{- end }}
{{- end -}}
{{- end -}}
