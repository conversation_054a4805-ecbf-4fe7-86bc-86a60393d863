{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.synapse -}}
{{- if .enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
{{- $extraAnnotations := dict }}
{{- if eq (include "element-io.ess-library.ingress-controller-type" (dict "root" $ "context" .ingress.controllerType)) "ingress-nginx" }}
{{- $_ := set $extraAnnotations "nginx.ingress.kubernetes.io/proxy-body-size" .media.maxUploadSize }}
{{- end }}
{{- include "element-io.ess-library.ingress.annotations" (dict "root" $ "context" (dict "ingress" .ingress "extraAnnotations" $extraAnnotations)) | nindent 2 }}
  labels:
    {{- include "element-io.synapse-ingress.labels" (dict "root" $ "context" $.Values.haproxy) | nindent 4 }}
  name: {{ $.Release.Name }}-synapse
  namespace: {{ $.Release.Namespace }}
spec:
{{- include "element-io.ess-library.ingress.tls" (dict "root" $ "context" (dict "ingress" .ingress "ingressName" "synapse")) | nindent 2 }}
{{- include "element-io.ess-library.ingress.className" (dict "root" $ "context" .ingress.className) | nindent 2 }}
  rules:
  - host: {{ (tpl .ingress.host $) | quote }}
    http:
      paths:
{{- range (include "element-io.synapse.ingress.additionalPaths" (dict "root" $ "context" .)) | fromYamlArray -}}
{{- if eq .availability "only_externally" }}
      - path: {{ .path }}
        pathType: Prefix
        backend:
          service:
            name: {{ (tpl .service.name $) | quote }}
            port:
              {{ .service.port | toYaml }}
{{- else if eq .availability "blocked" }}
      - path: {{ .path }}
        pathType: Prefix
        backend:
          service:
            name: "{{ $.Release.Name }}-synapse"
            port:
              name: haproxy-403
{{- end }}
{{- end }}
{{- range $synapsePath := (list "/_matrix" "/_synapse") -}}
{{- /* Determine if this path is equal to, or a subset of, one of the
   defined additional paths. If so, let the other service handle it and don't
   add it here. */}}
{{- $_pathAlreadyDefined := false }}
{{- range (include "element-io.synapse.ingress.additionalPaths" (dict "root" $ "context" .)) | fromYamlArray -}}
{{- if has .availability (list "only_externally" "blocked") }}
{{- if hasPrefix .path $synapsePath }}
{{- $_pathAlreadyDefined = true }}
{{- end }}
{{- end }}
{{- end -}}
{{- /* The path, or a superset of, has not already been defined in _additional_paths.
   Define it here.*/}}
{{- if not $_pathAlreadyDefined }}
      - path: {{ $synapsePath }}
        pathType: Prefix
        backend:
          service:
            name: "{{ $.Release.Name }}-synapse"
            port:
              name: haproxy-synapse
{{- end }}
{{- end }}
{{- end -}}
{{- end -}}
