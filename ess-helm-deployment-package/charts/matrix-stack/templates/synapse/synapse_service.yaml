{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if .enabled -}}
{{- range $processType := prepend (keys ((include "element-io.synapse.enabledWorkers" (dict "root" $)) | fromJson)) "main" }}
{{- $perProcessRoot := mustMergeOverwrite ($.Values.synapse | deepCopy) (dict "processType" $processType "isHook" false) }}
{{- $workerTypeName := include "element-io.synapse.process.workerTypeName" (dict "root" $ "context" $processType) }}
apiVersion: v1
kind: Service
metadata:
  labels:
    {{- include "element-io.synapse.process.labels" (dict "root" $ "context" $perProcessRoot) | nindent 4 }}
  name: {{ $.Release.Name }}-synapse-{{ $workerTypeName }}
  namespace: {{ $.Release.Namespace }}
spec:
  clusterIP: None
  ports:
{{- if (include "element-io.synapse.process.hasHttp" (dict "root" $ "context" .)) }}
  - name: synapse-http
    port: 8008
    targetPort: synapse-http
{{- end }}
{{- if (include "element-io.synapse.process.hasReplication" (dict "root" $ "context" .)) }}
  - name: synapse-repl
    port: 9093
    targetPort: synapse-repl
{{- end }}
  - name: synapse-health
    port: 8080
    targetPort: synapse-health
  - name: synapse-metrics
    port: 9001
    targetPort: synapse-metrics
  selector:
    app.kubernetes.io/instance: {{ $.Release.Name }}-synapse-{{ $processType }}
---
{{- end }}
{{- end -}}
{{- end -}}
