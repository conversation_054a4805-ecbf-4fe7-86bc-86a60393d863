{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if .enabled -}}
{{- if (include "element-io.synapse.enabledWorkers" (dict "root" $)) | from<PERSON>son }}
{{- with .redis -}}
apiVersion: v1
kind: Service
metadata:
  labels:
    {{- include "element-io.synapse-redis.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-synapse-redis
  namespace: {{ $.Release.Namespace }}
spec:
  ports:
  - port: 6379
    targetPort: redis
    name: redis
  selector:
    app.kubernetes.io/instance: "{{ $.Release.Name }}-synapse-redis"
{{- end }}
{{- end -}}
{{- end -}}
{{- end -}}
