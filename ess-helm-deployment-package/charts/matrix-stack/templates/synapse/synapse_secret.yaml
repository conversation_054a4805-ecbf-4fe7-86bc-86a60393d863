{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if .enabled -}}
apiVersion: v1
kind: Secret
type: Opaque
metadata:
  labels:
    {{- include "element-io.synapse.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ include "element-io.synapse.secret-name" (dict "root" $ "context" (dict "isHook" false)) }}
  namespace: {{ $.Release.Namespace }}
{{- include "element-io.synapse.secret-data"  (dict "root" $ "context" .) -}}
{{- end -}}
{{- end -}}
