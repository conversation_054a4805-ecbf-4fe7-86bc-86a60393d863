{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if and .enabled .checkConfigHook.enabled -}}
{{- $enabledWorkers := (include "element-io.synapse.enabledWorkers" (dict "root" $)) | from<PERSON>son }}
{{- $processType := "check-config" }}
{{- $perProcessRoot := mustMergeOverwrite ($.Values.synapse | deepCopy) (.checkConfigHook | deepCopy) (dict "processType" $processType "isHook" true) }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ $.Release.Name }}-synapse-check-config
  namespace: {{ $.Release.Namespace }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
{{- /*
Hook Weights are
- -10 : The initSecret hook generating secrets used by the check config job
- -5 : The synapse secret & configMap for the hook, so that they are created before the job
- 0 : The job itself, so that it is run after the secrets and configs are created &
*/}}
    "helm.sh/hook-weight": "0"
{{- with .checkConfigHook.annotations }}
    {{- toYaml . | nindent 4 }}
{{- end }}
  labels:
    {{- include "element-io.synapse-check-config.labels" (dict "root" $ "context" .checkConfigHook) | nindent 4 }}
    k8s.element.io/synapse-config-hash: "{{ include "element-io.synapse.configmap-data"  (dict "root" $ "context" $perProcessRoot) | sha1sum }}"
    k8s.element.io/synapse-secret-hash: "{{ include "element-io.synapse.secret-data"  (dict "root" $ "context" $perProcessRoot) | sha1sum }}"
{{- range $index, $appservice := .appservices }}
{{- if .configMap }}
    k8s.element.io/as-registration-{{ $index }}-hash: "{{ (lookup "v1" "ConfigMap" $.Release.Namespace (tpl $appservice.configMap $)) | toJson | sha1sum }}"
{{- else }}
    k8s.element.io/as-registration-{{ $index }}-hash: "{{ (lookup "v1" "Secret" $.Release.Namespace (tpl $appservice.secret $)) | toJson | sha1sum }}"
{{- end }}
{{- end }}
    {{ include "element-io.ess-library.postgres-label" (dict "root" $ "context" (dict
                                                            "essPassword" "synapse"
                                                            "postgresProperty" .postgres
                                                            )
                                        ) }}
spec:
  backoffLimit: 0
  completionMode: NonIndexed
  completions: 1
  manualSelector: false
  parallelism: 1
  podReplacementPolicy: TerminatingOrFailed
  {{- include "element-io.synapse.pod-template" (dict "root" $ "context" $perProcessRoot) | nindent 2 }}
{{- end }}
{{- end }}
