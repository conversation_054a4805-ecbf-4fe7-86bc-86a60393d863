{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}
{{- with .Values.matrixAuthenticationService -}}
{{- if and .enabled .syn2mas.enabled $.Values.synapse.enabled -}}
{{- with .syn2mas -}}
{{- include "element-io.ess-library.serviceAccount" (dict "root" $ "context" (dict "componentValues" . "nameSuffix" "syn2mas" "extraAnnotations" (dict "helm.sh/hook" "pre-install,pre-upgrade" "helm.sh/hook-weight" "0"))) }}
{{- end }}
{{- end }}
{{- end }}
