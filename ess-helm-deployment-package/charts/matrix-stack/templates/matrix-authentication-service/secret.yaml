{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.matrixAuthenticationService -}}
{{- if .enabled -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "element-io.matrix-authentication-service.secret-name" (dict "root" $ "context"  (dict "isHook" false)) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "element-io.matrix-authentication-service.labels" (dict "root" $ "context" .) | nindent 4 }}
type: Opaque
data:
{{- include "element-io.matrix-authentication-service.secret-data" (dict "root" $ "context" .) | nindent 2 }}
{{- end -}}
{{- end -}}
