{#
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
#}

{% import 'sub_schema_values.yaml.j2' as sub_schema_values -%}
enabled: true

rbac:
  create: true

{{- sub_schema_values.labels() -}}
{{- sub_schema_values.workloadAnnotations() -}}
{{- sub_schema_values.containersSecurityContext() -}}
{{- sub_schema_values.extraEnv() -}}
{{- sub_schema_values.nodeSelector() -}}
{{- sub_schema_values.podSecurityContext(user_id='10010', group_id='10010') -}}
{{- sub_schema_values.resources(requests_memory='50Mi', requests_cpu='50m', limits_memory='200Mi') -}}
{{- sub_schema_values.serviceAccount() -}}
{{- sub_schema_values.tolerations() -}}
