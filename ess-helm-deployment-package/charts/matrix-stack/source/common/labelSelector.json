{"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}}