# ESS-HELM 一键部署包

基于官方 element-hq/ess-helm 25.6.2 版本的完全独立部署包，严格遵循官方API规范和最佳实践。

## 🎯 核心特性

### 🚀 一键部署
- 支持通过 `bash <(curl -sSL <URL>/setup.sh)` 命令进行完全自动化部署
- 技术小白友好的交互式配置菜单系统
- 智能默认值处理，减少配置复杂度

### 🌐 Router WAN IP自动检测
- **检测间隔**: 5秒实时检测机制
- **API接口**: 基于MikroTik RouterOS官方API规范 (TCP:8728)
- **完全本地化**: 摒弃外部HTTP服务依赖
- **多接口支持**: ether1、pppoe-out1、lte1等

### 🔄 虚拟公网IP路由高可用
- **虚拟IP地址**: ********** (LiveKit)、********** (TURN)
- **适用范围**: 仅需要外部直接连接的服务
- **ESS内部服务**: 由ESS-HELM自动处理网络配置
- **零停机切换**: IP变化时仅更新路由表

### 🛠️ 增强管理功能
- **用户管理**: 基于Synapse Admin API的完整用户CRUD操作
- **服务控制**: 基于Kubernetes的服务启停、扩缩容、重启
- **注册控制**: 开放/关闭注册、注册令牌管理、邀请机制
- **运维管理**: 备份恢复、日志查看、监控告警

## 📁 项目结构

```
ess-helm-deployment-package/
├── setup.sh                           # 主入口脚本
├── scripts/
│   ├── external.sh                     # 外部服务器部署
│   ├── internal.sh                     # 内部服务器部署
│   ├── admin.sh                        # 管理脚本
│   ├── router-wan-ip-detector.sh       # Router WAN IP检测
│   └── test.sh                         # 功能验证测试
├── configs/                            # 配置文件目录
├── charts/
│   └── matrix-stack/                   # 官方Helm Chart (25.6.2)
├── docs/                               # 文档目录
├── official-ess-helm/                  # 官方源码参考
└── README.md                           # 项目说明
```

## 🚀 快速开始

### 方法一：一键部署 (推荐)

```bash
# 外部服务器部署 (有公网IP)
bash <(curl -sSL https://raw.githubusercontent.com/your-repo/ess-helm-deployment/main/setup.sh)

# 或者下载后本地执行
wget https://github.com/your-repo/ess-helm-deployment/archive/main.tar.gz
tar -xzf main.tar.gz
cd ess-helm-deployment-main
chmod +x setup.sh scripts/*.sh
./setup.sh
```

### 方法二：Git克隆

```bash
git clone https://github.com/your-repo/ess-helm-deployment.git
cd ess-helm-deployment
chmod +x setup.sh scripts/*.sh
./setup.sh
```

## 📋 部署选项

### 🌐 外部服务器部署
适用于有公网IP的服务器环境：
- 支持Router WAN IP自动检测
- 配置虚拟公网IP路由高可用
- 完整的ESS-HELM功能

### 🏠 内部服务器部署
适用于内网环境的服务器：
- 简化的网络配置
- 专注于ESS-HELM核心服务
- 优化的内网性能配置

## ⚙️ 配置说明

### 基础配置
- **Matrix服务器域名**: 您的Matrix服务器完整域名 (例: matrix.example.com)
- **子域名**: element、auth、rtc (可自定义)
- **端口配置**: HTTPS(443)、Matrix(8448)等
- **服务目录**: 服务主目录路径 (默认: /opt/ess-helm)

### 外部服务器特定配置
- **RouterOS设备IP**: RouterOS设备的IP地址
- **RouterOS API认证**: 用户名和密码
- **WAN接口**: WAN接口名称 (默认: ether1)
- **虚拟IP**: LiveKit(**********)、TURN(**********)

## 🔧 管理工具

### 启动管理控制台
```bash
./scripts/admin.sh
```

### 主要功能
1. **用户管理**: 创建、删除、修改用户，管理员权限控制
2. **房间管理**: 房间创建、删除、权限管理
3. **注册控制**: 开放/关闭注册，注册令牌管理
4. **系统状态**: 服务状态监控，版本信息查看

## 🧪 测试验证

### 运行功能测试
```bash
./scripts/test.sh
```

### 运行集成测试
```bash
./scripts/test.sh -i
# 或
RUN_INTEGRATION_TESTS=true ./scripts/test.sh
```

## 🔍 系统要求

### 基础要求
- Linux操作系统 (Ubuntu 20.04+, CentOS 8+, Debian 11+)
- 2GB+ RAM, 20GB+ 存储空间
- 网络连接 (用于下载依赖)

### 自动安装的依赖
- Docker
- Kubernetes (K3s)
- kubectl
- Helm
- Python3 (RouterOS API需要)
- jq (JSON处理)
- curl, wget等基础工具

### 外部服务器额外要求
- RouterOS设备 (支持API访问)
- 公网IP地址
- 防火墙端口开放

## 📚 技术规范

### 基于官方标准
- **element-hq/ess-helm**: 严格基于25.6.2稳定版本
- **Matrix.org**: 遵循Matrix协议和Synapse Admin API规范
- **MikroTik RouterOS**: 基于官方API文档 (TCP:8728)
- **Kubernetes**: 遵循官方Helm Chart最佳实践

### API规范遵循
- **Synapse Admin API**: `/_synapse/admin/v1/` 和 `/_synapse/admin/v2/`
- **RouterOS API**: 基于官方二进制协议实现
- **Matrix Client API**: `/_matrix/client/` 标准接口

## 🔐 安全注意事项

1. **管理员权限**: 部署过程需要sudo权限
2. **网络安全**: 确保防火墙配置正确
3. **密钥管理**: 妥善保管RouterOS API认证信息
4. **定期更新**: 定期更新系统和依赖
5. **备份策略**: 定期备份重要数据

## 🆘 故障排除

### 常见问题

**1. 依赖安装失败**
```bash
# 手动安装缺失的依赖
sudo apt-get update
sudo apt-get install -y curl wget git python3 jq
```

**2. RouterOS API连接失败**
```bash
# 检查网络连通性
ping $ROUTER_IP
# 检查API端口
nc -zv $ROUTER_IP 8728
```

**3. Kubernetes连接失败**
```bash
# 检查K3s状态
sudo systemctl status k3s
# 重启K3s
sudo systemctl restart k3s
```

### 获取帮助
- 查看日志: `/var/log/ess-*.log`
- 检查服务状态: `sudo systemctl status ess-*`
- 运行测试: `./scripts/test.sh -v`

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

### 开发环境设置
```bash
git clone https://github.com/your-repo/ess-helm-deployment.git
cd ess-helm-deployment
# 运行测试验证
./scripts/test.sh
```

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源。

## 🙏 致谢

- [Element](https://element.io/) - 提供优秀的Matrix客户端
- [element-hq/ess-helm](https://github.com/element-hq/ess-helm) - 官方Helm Chart
- [Matrix.org](https://matrix.org/) - Matrix协议和Synapse服务器
- [MikroTik](https://mikrotik.com/) - RouterOS API文档

---

**版本**: v1.0  
**更新日期**: 2025-06-19  
**基准版本**: element-hq/ess-helm 25.6.2  
**技术规范**: 严格遵循官方API文档和最佳实践
