#!/bin/bash
# ESS-HELM 功能验证测试脚本
# 基于官方API规范的测试验证

set -euo pipefail

# 脚本目录和配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$HOME/.ess-helm-config"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_test() { echo -e "${BLUE}[TEST]${NC} $1"; }
log_pass() { echo -e "${GREEN}[PASS]${NC} $1"; }
log_fail() { echo -e "${RED}[FAIL]${NC} $1"; }

# 测试结果记录
record_test_result() {
    local test_name="$1"
    local result="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ "$result" == "PASS" ]]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_pass "$test_name"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_fail "$test_name"
    fi
}

# 测试脚本文件存在性
test_script_files() {
    log_test "测试脚本文件存在性..."
    
    local required_scripts=(
        "setup.sh"
        "scripts/external.sh"
        "scripts/internal.sh"
        "scripts/admin.sh"
        "scripts/router-wan-ip-detector.sh"
    )
    
    local missing_files=()
    
    for script in "${required_scripts[@]}"; do
        local script_path="$PROJECT_DIR/$script"
        if [[ ! -f "$script_path" ]]; then
            missing_files+=("$script")
        fi
    done
    
    if [[ ${#missing_files[@]} -eq 0 ]]; then
        record_test_result "脚本文件存在性检查" "PASS"
    else
        log_error "缺少脚本文件: ${missing_files[*]}"
        record_test_result "脚本文件存在性检查" "FAIL"
    fi
}

# 测试官方Chart版本验证
test_official_chart_version() {
    log_test "测试官方Chart版本验证..."
    
    local chart_file="$PROJECT_DIR/charts/matrix-stack/Chart.yaml"
    if [[ ! -f "$chart_file" ]]; then
        record_test_result "官方Chart版本验证" "FAIL"
        return 1
    fi
    
    local chart_version
    chart_version=$(grep "^version:" "$chart_file" | cut -d' ' -f2 | sed 's/-dev//')
    
    if [[ "$chart_version" == "25.6.2" ]]; then
        record_test_result "官方Chart版本验证" "PASS"
    else
        log_error "Chart版本不匹配: 期望 25.6.2, 实际 $chart_version"
        record_test_result "官方Chart版本验证" "FAIL"
    fi
}

# 测试脚本语法
test_script_syntax() {
    log_test "测试脚本语法..."
    
    local scripts=(
        "$PROJECT_DIR/setup.sh"
        "$SCRIPT_DIR/external.sh"
        "$SCRIPT_DIR/internal.sh"
        "$SCRIPT_DIR/admin.sh"
        "$SCRIPT_DIR/router-wan-ip-detector.sh"
    )
    
    local syntax_errors=()
    
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]]; then
            if ! bash -n "$script" 2>/dev/null; then
                syntax_errors+=("$(basename "$script")")
            fi
        fi
    done
    
    if [[ ${#syntax_errors[@]} -eq 0 ]]; then
        record_test_result "脚本语法检查" "PASS"
    else
        log_error "脚本语法错误: ${syntax_errors[*]}"
        record_test_result "脚本语法检查" "FAIL"
    fi
}

# 测试系统依赖
test_system_dependencies() {
    log_test "测试系统依赖..."
    
    local required_deps=("curl" "wget" "git")
    local missing_deps=()
    
    for dep in "${required_deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -eq 0 ]]; then
        record_test_result "系统依赖检查" "PASS"
    else
        log_warn "缺少系统依赖: ${missing_deps[*]}"
        record_test_result "系统依赖检查" "FAIL"
    fi
}

# 测试Python3依赖 (RouterOS API需要)
test_python3_dependency() {
    log_test "测试Python3依赖..."
    
    if command -v python3 &> /dev/null; then
        # 测试socket模块
        if python3 -c "import socket, hashlib, binascii" 2>/dev/null; then
            record_test_result "Python3依赖检查" "PASS"
        else
            log_error "Python3缺少必需模块"
            record_test_result "Python3依赖检查" "FAIL"
        fi
    else
        log_warn "Python3未安装 (RouterOS API需要)"
        record_test_result "Python3依赖检查" "FAIL"
    fi
}

# 测试JSON处理依赖
test_json_dependencies() {
    log_test "测试JSON处理依赖..."
    
    if command -v jq &> /dev/null; then
        # 测试jq功能
        if echo '{"test": "value"}' | jq -r '.test' >/dev/null 2>&1; then
            record_test_result "JSON处理依赖检查" "PASS"
        else
            log_error "jq功能异常"
            record_test_result "JSON处理依赖检查" "FAIL"
        fi
    else
        log_warn "jq未安装 (Synapse Admin API需要)"
        record_test_result "JSON处理依赖检查" "FAIL"
    fi
}

# 测试网络连通性
test_network_connectivity() {
    log_test "测试网络连通性..."
    
    local test_urls=(
        "https://github.com"
        "https://get.helm.sh"
        "https://dl.k8s.io"
    )
    
    local failed_urls=()
    
    for url in "${test_urls[@]}"; do
        if ! curl -s --connect-timeout 5 --max-time 10 "$url" >/dev/null 2>&1; then
            failed_urls+=("$url")
        fi
    done
    
    if [[ ${#failed_urls[@]} -eq 0 ]]; then
        record_test_result "网络连通性检查" "PASS"
    else
        log_warn "无法访问: ${failed_urls[*]}"
        record_test_result "网络连通性检查" "FAIL"
    fi
}

# 测试配置文件生成
test_config_generation() {
    log_test "测试配置文件生成..."
    
    # 创建临时配置
    local temp_config="/tmp/test-ess-helm-config"
    cat > "$temp_config" << EOF
DEPLOYMENT_TYPE="external"
OFFICIAL_CHART_VERSION="25.6.2"
SERVER_NAME="matrix.test.com"
MAIN_DOMAIN="test.com"
ELEMENT_SUBDOMAIN="element"
AUTH_SUBDOMAIN="auth"
RTC_SUBDOMAIN="rtc"
HTTPS_PORT="443"
MATRIX_PORT="8448"
SERVICE_DIR="/opt/ess-helm"
SCRIPT_DIR="$PROJECT_DIR"
ROUTER_IP="***********"
ROUTER_USERNAME="admin"
ROUTER_PASSWORD="password"
WAN_INTERFACE="ether1"
LIVEKIT_VIRTUAL_IP="**********"
TURN_VIRTUAL_IP="**********"
EOF

    # 测试配置加载
    if source "$temp_config" 2>/dev/null; then
        record_test_result "配置文件生成测试" "PASS"
    else
        record_test_result "配置文件生成测试" "FAIL"
    fi
    
    # 清理临时文件
    rm -f "$temp_config"
}

# 测试RouterOS API连接格式
test_routeros_api_format() {
    log_test "测试RouterOS API连接格式..."
    
    # 测试Python RouterOS API代码语法
    if python3 -c "
import socket
import hashlib
import binascii

def encode_length(length):
    if length < 0x80:
        return bytes([length])
    return bytes([0xF0])

def encode_word(word):
    word_bytes = word.encode('utf-8')
    return encode_length(len(word_bytes)) + word_bytes

# 测试编码功能
test_word = encode_word('/login')
print('RouterOS API格式测试通过')
" 2>/dev/null; then
        record_test_result "RouterOS API格式测试" "PASS"
    else
        record_test_result "RouterOS API格式测试" "FAIL"
    fi
}

# 集成测试 (需要实际部署环境)
test_integration() {
    if [[ "${RUN_INTEGRATION_TESTS:-false}" != "true" ]]; then
        log_info "跳过集成测试 (设置 RUN_INTEGRATION_TESTS=true 启用)"
        return 0
    fi
    
    log_test "运行集成测试..."
    
    # 测试Kubernetes连接
    if command -v kubectl &> /dev/null && kubectl cluster-info &>/dev/null; then
        record_test_result "Kubernetes连接测试" "PASS"
    else
        record_test_result "Kubernetes连接测试" "FAIL"
    fi
    
    # 测试Helm功能
    if command -v helm &> /dev/null && helm version &>/dev/null; then
        record_test_result "Helm功能测试" "PASS"
    else
        record_test_result "Helm功能测试" "FAIL"
    fi
}

# 显示测试结果
show_test_results() {
    echo
    echo -e "${BLUE}=== 测试结果汇总 ===${NC}"
    echo "总测试数: $TOTAL_TESTS"
    echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败: ${RED}$FAILED_TESTS${NC}"
    
    local success_rate=0
    if [[ $TOTAL_TESTS -gt 0 ]]; then
        success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    fi
    
    echo "成功率: $success_rate%"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "${GREEN}🎉 所有测试通过！${NC}"
        return 0
    else
        echo -e "${RED}❌ 有测试失败，请检查上述错误信息${NC}"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "ESS-HELM 功能验证测试脚本"
    echo "基于官方API规范的测试验证"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -i, --integration       运行集成测试"
    echo "  -v, --verbose           详细输出"
    echo
    echo "环境变量:"
    echo "  RUN_INTEGRATION_TESTS   设置为 'true' 启用集成测试"
    echo
    echo "示例:"
    echo "  $0                      运行基础功能测试"
    echo "  $0 -i                   运行包括集成测试的完整测试"
}

# 主函数
main() {
    local run_integration=false
    local verbose=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -i|--integration)
                run_integration=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置集成测试环境变量
    if [[ "$run_integration" == "true" ]]; then
        export RUN_INTEGRATION_TESTS=true
    fi
    
    echo -e "${BLUE}=== ESS-HELM 功能验证测试开始 ===${NC}"
    echo "测试时间: $(date)"
    echo "项目目录: $PROJECT_DIR"
    echo "基于官方element-hq/ess-helm 25.6.2版本"
    echo
    
    # 运行测试
    test_script_files
    test_official_chart_version
    test_script_syntax
    test_system_dependencies
    test_python3_dependency
    test_json_dependencies
    test_network_connectivity
    test_config_generation
    test_routeros_api_format
    test_integration
    
    # 显示结果
    show_test_results
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
