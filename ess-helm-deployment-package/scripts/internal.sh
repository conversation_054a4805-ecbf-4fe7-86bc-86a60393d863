#!/bin/bash
# ESS-HELM 内部服务器部署脚本
# 基于官方element-hq/ess-helm 25.6.2版本
# 专为内网环境优化

set -euo pipefail

# 脚本目录和配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$HOME/.ess-helm-config"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 加载配置
load_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    source "$CONFIG_FILE"
    log_info "✅ 配置加载完成"
}

# 验证配置
validate_config() {
    log_info "验证内部服务器配置..."
    
    local required_vars=("SERVER_NAME" "HTTPS_PORT" "MATRIX_PORT")
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "缺少必需的配置变量: $var"
            exit 1
        fi
    done
    
    log_info "✅ 配置验证通过"
}

# 安装Kubernetes依赖
install_k8s_dependencies() {
    log_info "安装Kubernetes依赖..."
    
    # 安装kubectl
    if ! command -v kubectl &> /dev/null; then
        log_info "安装kubectl..."
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
        rm kubectl
    fi
    
    # 安装helm
    if ! command -v helm &> /dev/null; then
        log_info "安装Helm..."
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
    fi
    
    # 安装k3s
    if ! kubectl cluster-info &> /dev/null; then
        log_info "安装K3s..."
        curl -sfL https://get.k3s.io | sh -
        sudo chmod 644 /etc/rancher/k3s/k3s.yaml
        export KUBECONFIG=/etc/rancher/k3s/k3s.yaml
        echo 'export KUBECONFIG=/etc/rancher/k3s/k3s.yaml' >> ~/.bashrc
    fi
    
    log_info "✅ Kubernetes依赖安装完成"
}

# 生成内部服务器Helm values配置
generate_helm_values() {
    log_info "生成内部服务器Helm values配置..."
    
    local values_file="$PROJECT_DIR/configs/values-internal.yaml"
    mkdir -p "$(dirname "$values_file")"
    
    cat > "$values_file" << EOF
# ESS-HELM 内部服务器配置
# 基于官方element-hq/ess-helm ${OFFICIAL_CHART_VERSION}

global:
  serverName: "$SERVER_NAME"

synapse:
  enabled: true
  serverName: "$SERVER_NAME"
  config:
    enableRegistration: false
    registrationRequiresToken: true
    # 内网环境优化配置
    enableMetrics: true
    enablePresence: true
  
  # 内网环境资源配置
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "2Gi"
      cpu: "1000m"

elementWeb:
  enabled: true
  config:
    default_server_config:
      m.homeserver:
        base_url: "https://$SERVER_NAME:$MATRIX_PORT"
        server_name: "$SERVER_NAME"
    # 内网环境特定配置
    features:
      feature_new_spinner: true
      feature_pinning: true

matrixAuthenticationService:
  enabled: true
  config:
    http:
      public_base: "https://$AUTH_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT"

matrixRtc:
  enabled: true
  livekit:
    enabled: true
    # 内网环境RTC配置
    config:
      port_range_start: 30152
      port_range_end: 30200
      bind_addresses: ["0.0.0.0"]

postgresql:
  enabled: true
  auth:
    database: "synapse"
    username: "synapse_user"
  # 内网环境数据库配置
  primary:
    persistence:
      size: "20Gi"
    resources:
      requests:
        memory: "256Mi"
        cpu: "250m"
      limits:
        memory: "1Gi"
        cpu: "500m"

# 内网环境网络配置
service:
  type: ClusterIP
  ports:
    https: $HTTPS_PORT
    matrix: $MATRIX_PORT

# 内网环境Ingress配置
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: $ELEMENT_SUBDOMAIN.$MAIN_DOMAIN
      paths:
        - path: /
          pathType: Prefix
    - host: $SERVER_NAME
      paths:
        - path: /
          pathType: Prefix

# 内网环境存储配置
persistence:
  enabled: true
  storageClass: "local-path"
  accessMode: ReadWriteOnce
  size: "50Gi"

# 内网环境安全配置
networkPolicy:
  enabled: true
  ingress:
    enabled: true
  egress:
    enabled: true
    # 仅允许必要的出站连接
    allowedCIDRs:
      - "10.0.0.0/8"
      - "**********/12"
      - "***********/16"
EOF

    log_info "✅ 内部服务器Helm values配置生成完成"
}

# 安装Nginx Ingress Controller
install_nginx_ingress() {
    log_info "安装Nginx Ingress Controller..."
    
    # 检查是否已安装
    if kubectl get namespace ingress-nginx &> /dev/null; then
        log_info "Nginx Ingress Controller已存在"
        return 0
    fi
    
    # 安装Nginx Ingress Controller
    kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.2/deploy/static/provider/cloud/deploy.yaml
    
    # 等待Ingress Controller就绪
    kubectl wait --namespace ingress-nginx \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/component=controller \
        --timeout=300s
    
    log_info "✅ Nginx Ingress Controller安装完成"
}

# 部署ESS-HELM
deploy_ess_helm() {
    log_info "部署ESS-HELM..."
    
    local namespace="ess"
    local release_name="ess"
    local chart_path="$PROJECT_DIR/charts/matrix-stack"
    local values_file="$PROJECT_DIR/configs/values-internal.yaml"
    
    # 添加Helm仓库
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo update
    
    # 创建命名空间
    kubectl create namespace "$namespace" --dry-run=client -o yaml | kubectl apply -f -
    
    # 部署ESS-HELM
    helm upgrade --install "$release_name" "$chart_path" \
        --namespace "$namespace" \
        --values "$values_file" \
        --timeout 15m \
        --wait
    
    log_info "✅ ESS-HELM部署完成"
}

# 配置内网DNS解析
configure_internal_dns() {
    log_info "配置内网DNS解析..."
    
    # 获取Ingress IP
    local ingress_ip
    ingress_ip=$(kubectl get svc -n ingress-nginx ingress-nginx-controller -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    
    if [[ -z "$ingress_ip" ]]; then
        ingress_ip=$(kubectl get svc -n ingress-nginx ingress-nginx-controller -o jsonpath='{.spec.clusterIP}')
    fi
    
    if [[ -n "$ingress_ip" ]]; then
        log_info "Ingress IP: $ingress_ip"
        
        # 添加到/etc/hosts (仅用于测试)
        echo "# ESS-HELM 内网域名解析" | sudo tee -a /etc/hosts
        echo "$ingress_ip $ELEMENT_SUBDOMAIN.$MAIN_DOMAIN" | sudo tee -a /etc/hosts
        echo "$ingress_ip $SERVER_NAME" | sudo tee -a /etc/hosts
        echo "$ingress_ip $AUTH_SUBDOMAIN.$MAIN_DOMAIN" | sudo tee -a /etc/hosts
        
        log_info "✅ 内网DNS解析配置完成"
        log_warn "注意: 生产环境请配置内网DNS服务器"
    else
        log_warn "无法获取Ingress IP，请手动配置DNS解析"
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    local namespace="ess"
    
    # 检查Pod状态
    log_info "检查Pod状态..."
    kubectl get pods -n "$namespace"
    
    # 检查服务状态
    log_info "检查服务状态..."
    kubectl get svc -n "$namespace"
    
    # 检查Ingress状态
    log_info "检查Ingress状态..."
    kubectl get ingress -n "$namespace"
    
    log_info "✅ 部署验证完成"
}

# 显示部署结果
show_deployment_result() {
    echo
    echo -e "${GREEN}🎉 ESS-HELM 内部服务器部署完成！${NC}"
    echo
    echo -e "${BLUE}=== 访问信息 ===${NC}"
    echo "Matrix服务器: https://$SERVER_NAME:$MATRIX_PORT"
    echo "Element Web: https://$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT"
    echo "认证服务: https://$AUTH_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT"
    echo
    echo -e "${BLUE}=== 管理工具 ===${NC}"
    echo "管理控制台: $SCRIPT_DIR/admin.sh"
    echo "配置文件: $CONFIG_FILE"
    echo
    echo -e "${BLUE}=== Kubernetes信息 ===${NC}"
    echo "命名空间: ess"
    echo "查看Pod状态: kubectl get pods -n ess"
    echo "查看服务状态: kubectl get svc -n ess"
    echo "查看日志: kubectl logs -n ess -l app.kubernetes.io/component=matrix-server"
    echo
    echo -e "${YELLOW}注意: 内网环境已配置本地DNS解析，生产环境请配置内网DNS服务器${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}=== ESS-HELM 内部服务器部署开始 ===${NC}"
    
    # 加载和验证配置
    load_config
    validate_config
    
    # 安装依赖
    install_k8s_dependencies
    
    # 生成配置
    generate_helm_values
    
    # 安装Nginx Ingress
    install_nginx_ingress
    
    # 部署ESS-HELM
    deploy_ess_helm
    
    # 配置内网DNS
    configure_internal_dns
    
    # 验证部署
    verify_deployment
    
    # 显示结果
    show_deployment_result
    
    echo -e "${GREEN}✅ 内部服务器部署流程完成${NC}"
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
