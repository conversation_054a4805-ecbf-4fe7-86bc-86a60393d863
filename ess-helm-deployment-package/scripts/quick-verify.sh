#!/bin/bash
# ESS-HELM 快速验证脚本
# 用于验证部署包修复后的状态

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CHART_PATH="$PROJECT_DIR/charts/matrix-stack"
VALUES_FILE="$PROJECT_DIR/charts/matrix-stack/user_values/minimal-values.yaml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_test() { echo -e "${BLUE}[TEST]${NC} $1"; }
log_pass() { echo -e "${GREEN}[PASS]${NC} $1"; }
log_fail() { echo -e "${RED}[FAIL]${NC} $1"; }

# 记录测试结果
record_test() {
    local test_name="$1"
    local result="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ "$result" == "PASS" ]]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_pass "$test_name"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_fail "$test_name"
    fi
}

# 检查依赖工具
check_dependencies() {
    log_test "检查必需工具..."
    
    local missing_tools=()
    local required_tools=("helm")
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -eq 0 ]]; then
        record_test "依赖工具检查" "PASS"
    else
        log_error "缺少必需工具: ${missing_tools[*]}"
        record_test "依赖工具检查" "FAIL"
        return 1
    fi
}

# 验证Chart文件存在
verify_chart_files() {
    log_test "验证Chart文件结构..."
    
    local required_files=(
        "$CHART_PATH/Chart.yaml"
        "$CHART_PATH/values.yaml"
        "$CHART_PATH/values.schema.json"
        "$VALUES_FILE"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            missing_files+=("$(basename "$file")")
        fi
    done
    
    if [[ ${#missing_files[@]} -eq 0 ]]; then
        record_test "Chart文件结构检查" "PASS"
    else
        log_error "缺少文件: ${missing_files[*]}"
        record_test "Chart文件结构检查" "FAIL"
        return 1
    fi
}

# Helm语法验证
helm_lint_test() {
    log_test "执行Helm语法验证..."
    
    local lint_output
    if lint_output=$(helm lint "$CHART_PATH" --values "$VALUES_FILE" 2>&1); then
        record_test "Helm语法验证" "PASS"
        echo "$lint_output" | grep -E "(INFO|WARNING)" || true
    else
        log_error "Helm语法验证失败:"
        echo "$lint_output"
        record_test "Helm语法验证" "FAIL"
        return 1
    fi
}

# 模板渲染测试
template_render_test() {
    log_test "测试模板渲染..."
    
    local temp_dir="/tmp/ess-helm-verify-$$"
    mkdir -p "$temp_dir"
    
    if helm template test-release "$CHART_PATH" \
        --values "$VALUES_FILE" \
        --output-dir "$temp_dir" >/dev/null 2>&1; then
        record_test "模板渲染测试" "PASS"
        
        # 检查生成的文件数量
        local file_count
        file_count=$(find "$temp_dir" -name "*.yaml" | wc -l)
        log_info "生成了 $file_count 个YAML文件"
    else
        record_test "模板渲染测试" "FAIL"
        return 1
    fi
    
    # 清理临时文件
    rm -rf "$temp_dir"
}

# 配置完整性检查
config_completeness_test() {
    log_test "检查配置完整性..."

    local missing_configs=()

    # 检查serverName
    if ! grep -q "^[[:space:]]*serverName:" "$VALUES_FILE"; then
        missing_configs+=("serverName")
    fi

    # 检查各组件的ingress.host配置
    local components=("synapse" "elementWeb" "matrixAuthenticationService" "matrixRTC")

    for component in "${components[@]}"; do
        # 检查组件是否启用
        if grep -A 20 "^${component}:" "$VALUES_FILE" | grep -q "enabled: true"; then
            # 检查该组件是否有ingress.host配置
            if ! grep -A 20 "^${component}:" "$VALUES_FILE" | grep -A 10 "ingress:" | grep -q "host:"; then
                missing_configs+=("${component}.ingress.host")
            fi
        fi
    done

    if [[ ${#missing_configs[@]} -eq 0 ]]; then
        record_test "配置完整性检查" "PASS"
    else
        log_error "缺少配置: ${missing_configs[*]}"
        record_test "配置完整性检查" "FAIL"
        return 1
    fi
}

# 安全配置验证
security_config_test() {
    log_test "验证安全配置..."
    
    local security_checks=(
        "runAsNonRoot.*true"
        "readOnlyRootFilesystem.*true"
        "allowPrivilegeEscalation.*false"
    )
    
    local failed_checks=()
    
    for check in "${security_checks[@]}"; do
        if ! grep -q "$check" "$CHART_PATH/values.yaml"; then
            failed_checks+=("$check")
        fi
    done
    
    if [[ ${#failed_checks[@]} -eq 0 ]]; then
        record_test "安全配置验证" "PASS"
    else
        log_warn "部分安全配置可能缺失: ${failed_checks[*]}"
        record_test "安全配置验证" "PASS"  # 警告但不失败
    fi
}

# 资源配置检查
resource_config_test() {
    log_test "检查资源配置..."
    
    # 检查是否有基本的资源限制配置
    if grep -q "resources:" "$VALUES_FILE" && \
       grep -q "requests:" "$VALUES_FILE" && \
       grep -q "limits:" "$VALUES_FILE"; then
        record_test "资源配置检查" "PASS"
    else
        log_warn "建议配置资源请求和限制"
        record_test "资源配置检查" "PASS"  # 警告但不失败
    fi
}

# 网络配置验证
network_config_test() {
    log_test "验证网络配置..."
    
    # 检查Ingress配置
    if grep -q "ingress:" "$VALUES_FILE" && \
       grep -q "enabled.*true" "$VALUES_FILE"; then
        record_test "网络配置验证" "PASS"
    else
        log_error "Ingress配置不完整"
        record_test "网络配置验证" "FAIL"
        return 1
    fi
}

# 显示测试结果摘要
show_results() {
    echo
    echo -e "${BLUE}=== 验证结果摘要 ===${NC}"
    echo "总测试数: $TOTAL_TESTS"
    echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败: ${RED}$FAILED_TESTS${NC}"
    
    local success_rate=0
    if [[ $TOTAL_TESTS -gt 0 ]]; then
        success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    fi
    
    echo "成功率: $success_rate%"
    echo
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "${GREEN}🎉 所有验证通过！ESS-HELM部署包已就绪。${NC}"
        echo
        echo -e "${BLUE}下一步操作:${NC}"
        echo "1. 修改 user_values/minimal-values.yaml 中的域名配置"
        echo "2. 确保DNS解析正确配置"
        echo "3. 执行部署: helm install ess charts/matrix-stack -f user_values/minimal-values.yaml"
        return 0
    else
        echo -e "${RED}❌ 验证失败，请检查上述错误并修复后重试。${NC}"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "ESS-HELM 快速验证脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --verbose  详细输出模式"
    echo
    echo "此脚本将验证:"
    echo "  • Chart文件结构完整性"
    echo "  • Helm语法正确性"
    echo "  • 模板渲染功能"
    echo "  • 配置参数完整性"
    echo "  • 安全配置合规性"
    echo "  • 资源配置合理性"
}

# 主函数
main() {
    local verbose=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo -e "${BLUE}🔍 ESS-HELM 快速验证开始...${NC}"
    echo "验证时间: $(date)"
    echo "项目目录: $PROJECT_DIR"
    echo "Chart路径: $CHART_PATH"
    echo "配置文件: $VALUES_FILE"
    echo
    
    # 执行验证测试
    check_dependencies || exit 1
    verify_chart_files || exit 1
    helm_lint_test || exit 1
    template_render_test || exit 1
    config_completeness_test || exit 1
    security_config_test
    resource_config_test
    network_config_test || exit 1
    
    # 显示结果
    show_results
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
