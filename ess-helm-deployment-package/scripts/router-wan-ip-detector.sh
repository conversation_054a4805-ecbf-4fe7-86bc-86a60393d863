#!/bin/bash
# Router WAN IP检测脚本
# 基于MikroTik RouterOS官方API规范 (TCP:8728)
# 严格遵循官方API协议实现

set -euo pipefail

# 配置文件和日志
CONFIG_FILE="${CONFIG_FILE:-$HOME/.ess-helm-config}"
LOG_FILE="/var/log/ess-router-wan-ip.log"
CURRENT_IP_FILE="/tmp/ess-current-wan-ip"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 日志函数
log_with_timestamp() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

log_info() { log_with_timestamp "[INFO] $1"; }
log_warn() { log_with_timestamp "[WARN] $1"; }
log_error() { log_with_timestamp "[ERROR] $1"; }

# 加载配置
load_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    source "$CONFIG_FILE"
    
    # 验证必需的配置变量
    local required_vars=("ROUTER_IP" "ROUTER_USERNAME" "ROUTER_PASSWORD" "WAN_INTERFACE")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "缺少必需的配置变量: $var"
            exit 1
        fi
    done
    
    log_info "配置加载完成 - Router: $ROUTER_IP, Interface: $WAN_INTERFACE"
}

# RouterOS API连接函数 - 基于官方API协议
routeros_api_query() {
    local host="$1"
    local username="$2"
    local password="$3"
    local interface="$4"
    local port="${5:-8728}"
    
    # 使用Python实现RouterOS API客户端 - 基于官方Python示例
    python3 << EOF
import socket
import hashlib
import binascii
import sys

def encode_length(length):
    """根据官方API规范编码长度"""
    if length < 0x80:
        return bytes([length])
    elif length < 0x4000:
        return bytes([(length >> 8) | 0x80, length & 0xFF])
    elif length < 0x200000:
        return bytes([(length >> 16) | 0xC0, (length >> 8) & 0xFF, length & 0xFF])
    elif length < 0x10000000:
        return bytes([(length >> 24) | 0xE0, (length >> 16) & 0xFF, (length >> 8) & 0xFF, length & 0xFF])
    else:
        return bytes([0xF0, (length >> 24) & 0xFF, (length >> 16) & 0xFF, (length >> 8) & 0xFF, length & 0xFF])

def encode_word(word):
    """编码API单词"""
    word_bytes = word.encode('utf-8')
    return encode_length(len(word_bytes)) + word_bytes

def decode_length(data, offset):
    """解码长度"""
    first_byte = data[offset]
    if first_byte < 0x80:
        return first_byte, offset + 1
    elif first_byte < 0xC0:
        return ((first_byte & 0x7F) << 8) + data[offset + 1], offset + 2
    elif first_byte < 0xE0:
        return ((first_byte & 0x3F) << 16) + (data[offset + 1] << 8) + data[offset + 2], offset + 3
    elif first_byte < 0xF0:
        return ((first_byte & 0x1F) << 24) + (data[offset + 1] << 16) + (data[offset + 2] << 8) + data[offset + 3], offset + 4
    else:
        return (data[offset + 1] << 24) + (data[offset + 2] << 16) + (data[offset + 3] << 8) + data[offset + 4], offset + 5

def read_sentence(sock):
    """读取API句子"""
    words = []
    while True:
        # 读取长度
        length_data = sock.recv(1)
        if not length_data:
            break
        
        length, _ = decode_length(length_data + sock.recv(4), 0)
        if length == 0:
            break
        
        # 读取单词内容
        word_data = b''
        while len(word_data) < length:
            chunk = sock.recv(length - len(word_data))
            if not chunk:
                break
            word_data += chunk
        
        words.append(word_data.decode('utf-8'))
    
    return words

def send_sentence(sock, words):
    """发送API句子"""
    for word in words:
        sock.send(encode_word(word))
    sock.send(encode_length(0))  # 句子结束标记

try:
    # 连接到RouterOS API
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(10)
    sock.connect(('$host', $port))
    
    # 登录 - 基于官方post-v6.43登录方法
    send_sentence(sock, ['/login', '=name=$username', '=password=$password'])
    response = read_sentence(sock)
    
    if not response or response[0] != '!done':
        print("LOGIN_FAILED")
        sys.exit(1)
    
    # 查询接口IP地址 - 基于官方API命令
    send_sentence(sock, ['/ip/address/print', '=.proplist=address,interface', '=?interface=$interface'])
    
    wan_ip = None
    while True:
        response = read_sentence(sock)
        if not response:
            break
        
        if response[0] == '!re':
            # 解析响应数据
            for item in response[1:]:
                if item.startswith('=address='):
                    address = item.split('=', 2)[2]
                    # 提取IP地址（去除子网掩码）
                    wan_ip = address.split('/')[0]
                    break
        elif response[0] == '!done':
            break
        elif response[0] == '!trap':
            print("API_ERROR")
            sys.exit(1)
    
    sock.close()
    
    if wan_ip:
        print(wan_ip)
    else:
        print("NO_IP_FOUND")

except Exception as e:
    print(f"CONNECTION_ERROR: {e}")
    sys.exit(1)
EOF
}

# 验证IP地址格式
validate_ip() {
    local ip="$1"
    if [[ "$ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        # 验证每个八位组
        IFS='.' read -ra ADDR <<< "$ip"
        for i in "${ADDR[@]}"; do
            if [[ $i -gt 255 ]]; then
                return 1
            fi
        done
        return 0
    fi
    return 1
}

# 检查IP是否变化
check_ip_change() {
    local new_ip="$1"
    local current_ip=""
    
    if [[ -f "$CURRENT_IP_FILE" ]]; then
        current_ip=$(cat "$CURRENT_IP_FILE" 2>/dev/null || echo "")
    fi
    
    if [[ "$new_ip" != "$current_ip" ]]; then
        echo "$new_ip" > "$CURRENT_IP_FILE"
        return 0  # IP已变化
    fi
    
    return 1  # IP未变化
}

# 触发IP变化事件
trigger_ip_change_event() {
    local old_ip="$1"
    local new_ip="$2"
    
    log_info "检测到WAN IP变化: $old_ip -> $new_ip"
    
    # 触发虚拟IP路由更新
    echo "$new_ip" > "/tmp/ess-wan-ip-change.trigger"
    
    # 发送系统通知
    logger "ESS-HELM: WAN IP changed from $old_ip to $new_ip"
    
    # 通知虚拟IP路由管理器
    if systemctl is-active --quiet ess-virtual-ip-routing; then
        systemctl reload ess-virtual-ip-routing || true
    fi
}

# 获取WAN IP
get_wan_ip() {
    local wan_ip
    wan_ip=$(routeros_api_query "$ROUTER_IP" "$ROUTER_USERNAME" "$ROUTER_PASSWORD" "$WAN_INTERFACE")
    
    case "$wan_ip" in
        "LOGIN_FAILED")
            log_error "RouterOS API登录失败"
            return 1
            ;;
        "API_ERROR")
            log_error "RouterOS API查询错误"
            return 1
            ;;
        "NO_IP_FOUND")
            log_warn "未找到接口 $WAN_INTERFACE 的IP地址"
            return 1
            ;;
        "CONNECTION_ERROR:"*)
            log_error "RouterOS API连接错误: ${wan_ip#CONNECTION_ERROR: }"
            return 1
            ;;
        *)
            if validate_ip "$wan_ip"; then
                echo "$wan_ip"
                return 0
            else
                log_error "获取到无效的IP地址: $wan_ip"
                return 1
            fi
            ;;
    esac
}

# 主检测循环
main_detection_loop() {
    log_info "启动WAN IP检测循环 (5秒间隔)"
    
    local consecutive_failures=0
    local max_failures=12  # 1分钟内最多失败12次
    
    while true; do
        local wan_ip
        if wan_ip=$(get_wan_ip); then
            consecutive_failures=0
            
            # 检查IP是否变化
            local old_ip=""
            if [[ -f "$CURRENT_IP_FILE" ]]; then
                old_ip=$(cat "$CURRENT_IP_FILE" 2>/dev/null || echo "")
            fi
            
            if check_ip_change "$wan_ip"; then
                trigger_ip_change_event "$old_ip" "$wan_ip"
            else
                # 每分钟记录一次当前IP
                if [[ $(($(date +%s) % 60)) -eq 0 ]]; then
                    log_info "当前WAN IP: $wan_ip"
                fi
            fi
        else
            consecutive_failures=$((consecutive_failures + 1))
            log_error "WAN IP检测失败 ($consecutive_failures/$max_failures)"
            
            if [[ $consecutive_failures -ge $max_failures ]]; then
                log_error "连续检测失败次数过多，退出检测循环"
                exit 1
            fi
        fi
        
        # 等待5秒
        sleep 5
    done
}

# 检查Python3依赖
check_python3() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装，RouterOS API需要Python3支持"
        log_info "正在尝试安装Python3..."
        
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y python3
        elif command -v yum &> /dev/null; then
            sudo yum install -y python3
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y python3
        else
            log_error "无法自动安装Python3，请手动安装"
            exit 1
        fi
    fi
}

# 信号处理
cleanup() {
    log_info "收到退出信号，清理资源..."
    exit 0
}

# 主函数
main() {
    # 设置信号处理
    trap cleanup SIGTERM SIGINT
    
    # 创建日志目录
    sudo mkdir -p "$(dirname "$LOG_FILE")" 2>/dev/null || mkdir -p "$(dirname "$LOG_FILE")"
    touch "$LOG_FILE" 2>/dev/null || true
    
    log_info "=== ESS-HELM Router WAN IP检测器启动 ==="
    log_info "PID: $$"
    log_info "基于RouterOS官方API规范 (TCP:8728)"
    
    # 检查依赖
    check_python3
    
    # 加载配置
    load_config
    
    # 启动检测循环
    main_detection_loop
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
