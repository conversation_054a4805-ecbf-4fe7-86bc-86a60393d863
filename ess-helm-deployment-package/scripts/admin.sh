#!/bin/bash
# ESS-HELM 管理脚本
# 基于官方Synapse Admin API规范
# 严格遵循Matrix.org官方API文档

set -euo pipefail

# 配置文件
CONFIG_FILE="${CONFIG_FILE:-$HOME/.ess-helm-config}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 加载配置
load_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        log_error "请先运行 setup.sh 进行配置"
        exit 1
    fi
    
    source "$CONFIG_FILE"
    
    # 设置API端点 - 基于官方Synapse Admin API规范
    SYNAPSE_ADMIN_API="http://localhost:8008/_synapse/admin"
    MATRIX_API="http://localhost:8008/_matrix"
    
    log_info "配置加载完成 - Matrix服务器: $SERVER_NAME"
}

# 获取管理员访问令牌 - 基于官方文档
get_admin_token() {
    if [[ -n "${SYNAPSE_ADMIN_TOKEN:-}" ]]; then
        echo "$SYNAPSE_ADMIN_TOKEN"
        return 0
    fi
    
    # 尝试从Kubernetes Secret获取
    if command -v kubectl &> /dev/null; then
        local token
        token=$(kubectl get secret -n ess ess-synapse-admin-token -o jsonpath='{.data.token}' 2>/dev/null | base64 -d 2>/dev/null || echo "")
        if [[ -n "$token" ]]; then
            echo "$token"
            return 0
        fi
    fi
    
    log_error "未找到管理员访问令牌"
    log_error "请设置环境变量 SYNAPSE_ADMIN_TOKEN 或确保Kubernetes Secret存在"
    return 1
}

# API调用函数 - 基于官方Admin API规范
api_call() {
    local method="$1"
    local endpoint="$2"
    local data="${3:-}"
    
    local token
    if ! token=$(get_admin_token); then
        return 1
    fi
    
    local curl_args=(
        -s
        -X "$method"
        -H "Authorization: Bearer $token"
        -H "Content-Type: application/json"
    )
    
    if [[ -n "$data" ]]; then
        curl_args+=(-d "$data")
    fi
    
    curl "${curl_args[@]}" "$endpoint"
}

# 显示主菜单
show_main_menu() {
    clear
    echo -e "${BLUE}=== ESS-HELM 管理控制台 ===${NC}"
    echo -e "${BLUE}基于官方Synapse Admin API${NC}"
    echo
    echo "1. 👥 用户管理"
    echo "2. 🏠 房间管理"
    echo "3. 🔐 注册控制"
    echo "4. 📊 系统状态"
    echo "5. 🔧 服务控制"
    echo "0. 🚪 退出"
    echo
    echo "=================================="
}

# 用户管理菜单
show_user_menu() {
    clear
    echo -e "${BLUE}=== 用户管理 ===${NC}"
    echo
    echo "1. 📋 列出所有用户"
    echo "2. ➕ 创建用户"
    echo "3. 🔍 查询用户信息"
    echo "4. 🗑️  删除用户"
    echo "5. 👑 设置管理员权限"
    echo "0. 🔙 返回主菜单"
    echo
}

# 列出用户 - 基于官方API: GET /_synapse/admin/v2/users
list_users() {
    log_info "获取用户列表..."
    
    local response
    response=$(api_call "GET" "$SYNAPSE_ADMIN_API/v2/users?limit=100")
    
    if [[ $? -eq 0 ]]; then
        echo "$response" | jq -r '.users[] | "\(.name) - \(.displayname // "无显示名") - 管理员: \(.admin)"' 2>/dev/null || {
            log_error "解析用户列表失败"
            echo "$response"
        }
    else
        log_error "获取用户列表失败"
    fi
}

# 创建用户 - 基于官方API: PUT /_synapse/admin/v2/users/<user_id>
create_user() {
    echo -e "${CYAN}=== 创建新用户 ===${NC}"
    
    read -p "用户名 (不含@和域名): " username
    read -p "显示名称: " displayname
    read -s -p "密码: " password
    echo
    read -p "是否为管理员? (y/n): " is_admin
    
    local user_id="@${username}:${SERVER_NAME}"
    local admin_flag="false"
    [[ "$is_admin" == "y" || "$is_admin" == "Y" ]] && admin_flag="true"
    
    local data
    data=$(jq -n \
        --arg password "$password" \
        --arg displayname "$displayname" \
        --argjson admin "$admin_flag" \
        '{password: $password, displayname: $displayname, admin: $admin}')
    
    log_info "创建用户: $user_id"
    
    local response
    response=$(api_call "PUT" "$SYNAPSE_ADMIN_API/v2/users/$(echo "$user_id" | jq -rR @uri)" "$data")
    
    if echo "$response" | jq -e '.name' >/dev/null 2>&1; then
        log_info "✅ 用户创建成功"
        echo "$response" | jq .
    else
        log_error "❌ 用户创建失败"
        echo "$response"
    fi
}

# 查询用户信息 - 基于官方API: GET /_synapse/admin/v2/users/<user_id>
query_user() {
    read -p "请输入用户ID (例: @user:domain.com): " user_id
    
    log_info "查询用户信息: $user_id"
    
    local response
    response=$(api_call "GET" "$SYNAPSE_ADMIN_API/v2/users/$(echo "$user_id" | jq -rR @uri)")
    
    if echo "$response" | jq -e '.name' >/dev/null 2>&1; then
        echo "$response" | jq .
    else
        log_error "用户不存在或查询失败"
        echo "$response"
    fi
}

# 删除用户 - 基于官方API: POST /_synapse/admin/v1/deactivate/<user_id>
delete_user() {
    read -p "请输入要删除的用户ID: " user_id
    read -p "确认删除用户 $user_id? (yes/no): " confirm
    
    if [[ "$confirm" != "yes" ]]; then
        log_info "操作已取消"
        return 0
    fi
    
    log_info "删除用户: $user_id"
    
    local data='{"erase": true}'
    local response
    response=$(api_call "POST" "$SYNAPSE_ADMIN_API/v1/deactivate/$(echo "$user_id" | jq -rR @uri)" "$data")
    
    if echo "$response" | jq -e '.id_server_unbind_result' >/dev/null 2>&1; then
        log_info "✅ 用户删除成功"
    else
        log_error "❌ 用户删除失败"
        echo "$response"
    fi
}

# 设置管理员权限 - 基于官方API: PUT /_synapse/admin/v2/users/<user_id>
set_admin() {
    read -p "请输入用户ID: " user_id
    read -p "设置为管理员? (y/n): " is_admin
    
    local admin_flag="false"
    [[ "$is_admin" == "y" || "$is_admin" == "Y" ]] && admin_flag="true"
    
    local data
    data=$(jq -n --argjson admin "$admin_flag" '{admin: $admin}')
    
    log_info "更新用户权限: $user_id"
    
    local response
    response=$(api_call "PUT" "$SYNAPSE_ADMIN_API/v2/users/$(echo "$user_id" | jq -rR @uri)" "$data")
    
    if echo "$response" | jq -e '.name' >/dev/null 2>&1; then
        log_info "✅ 权限更新成功"
        echo "$response" | jq '{name, admin}'
    else
        log_error "❌ 权限更新失败"
        echo "$response"
    fi
}

# 系统状态 - 基于官方API: GET /_synapse/admin/v1/server_version
show_system_status() {
    clear
    echo -e "${BLUE}=== 系统状态 ===${NC}"
    echo
    
    # Synapse版本信息
    log_info "获取Synapse版本信息..."
    local version_response
    version_response=$(api_call "GET" "$SYNAPSE_ADMIN_API/v1/server_version")
    
    if echo "$version_response" | jq -e '.server_version' >/dev/null 2>&1; then
        echo "Synapse版本: $(echo "$version_response" | jq -r '.server_version')"
        echo "Python版本: $(echo "$version_response" | jq -r '.python_version')"
    else
        log_error "无法获取版本信息"
    fi
    
    echo
    
    # Kubernetes Pod状态
    if command -v kubectl &> /dev/null; then
        log_info "Kubernetes Pod状态:"
        kubectl get pods -n ess 2>/dev/null || log_warn "无法获取Pod状态"
    fi
    
    echo
    read -p "按回车键继续..."
}

# 服务控制
show_service_menu() {
    clear
    echo -e "${BLUE}=== 服务控制 ===${NC}"
    echo
    echo "1. 📊 查看Pod状态"
    echo "2. 🔄 重启Synapse"
    echo "3. 📈 扩缩容服务"
    echo "4. 📋 查看日志"
    echo "0. 🔙 返回主菜单"
    echo
}

# 用户管理主循环
user_management() {
    while true; do
        show_user_menu
        read -p "请选择操作 [1-5, 0返回]: " choice
        
        case "$choice" in
            1) list_users; read -p "按回车键继续..." ;;
            2) create_user; read -p "按回车键继续..." ;;
            3) query_user; read -p "按回车键继续..." ;;
            4) delete_user; read -p "按回车键继续..." ;;
            5) set_admin; read -p "按回车键继续..." ;;
            0) return 0 ;;
            *) log_warn "无效选择"; read -p "按回车键继续..." ;;
        esac
    done
}

# 检查依赖
check_dependencies() {
    local missing_deps=()
    
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi
    
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少必需的依赖: ${missing_deps[*]}"
        log_info "正在尝试安装..."
        
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y "${missing_deps[@]}"
        elif command -v yum &> /dev/null; then
            sudo yum install -y "${missing_deps[@]}"
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y "${missing_deps[@]}"
        else
            log_error "无法自动安装依赖，请手动安装: ${missing_deps[*]}"
            exit 1
        fi
    fi
}

# 主程序循环
main() {
    # 检查依赖
    check_dependencies
    
    # 加载配置
    load_config
    
    # 验证API连接
    if ! get_admin_token >/dev/null; then
        log_error "无法获取管理员令牌，请检查配置"
        exit 1
    fi
    
    while true; do
        show_main_menu
        read -p "请选择操作 [1-5, 0退出]: " choice
        
        case "$choice" in
            1) user_management ;;
            2) log_info "房间管理功能开发中..."; read -p "按回车键继续..." ;;
            3) log_info "注册控制功能开发中..."; read -p "按回车键继续..." ;;
            4) show_system_status ;;
            5) log_info "服务控制功能开发中..."; read -p "按回车键继续..." ;;
            0) log_info "感谢使用ESS-HELM管理控制台！"; exit 0 ;;
            *) log_warn "无效选择，请重试"; read -p "按回车键继续..." ;;
        esac
    done
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
