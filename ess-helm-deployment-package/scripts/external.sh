#!/bin/bash
# ESS-HELM 外部服务器部署脚本
# 基于官方element-hq/ess-helm 25.6.2版本
# 支持Router WAN IP检测和虚拟公网IP路由

set -euo pipefail

# 脚本目录和配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$HOME/.ess-helm-config"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 加载配置
load_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    source "$CONFIG_FILE"
    log_info "✅ 配置加载完成"
}

# 验证配置
validate_config() {
    log_info "验证外部服务器配置..."
    
    local required_vars=(
        "SERVER_NAME" "ROUTER_IP" "ROUTER_USERNAME" 
        "LIVEKIT_VIRTUAL_IP" "TURN_VIRTUAL_IP"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "缺少必需的配置变量: $var"
            exit 1
        fi
    done
    
    log_info "✅ 配置验证通过"
}

# 安装Kubernetes依赖
install_k8s_dependencies() {
    log_info "安装Kubernetes依赖..."
    
    # 安装kubectl
    if ! command -v kubectl &> /dev/null; then
        log_info "安装kubectl..."
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
        rm kubectl
    fi
    
    # 安装helm
    if ! command -v helm &> /dev/null; then
        log_info "安装Helm..."
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
    fi
    
    # 安装k3s
    if ! kubectl cluster-info &> /dev/null; then
        log_info "安装K3s..."
        curl -sfL https://get.k3s.io | sh -
        sudo chmod 644 /etc/rancher/k3s/k3s.yaml
        export KUBECONFIG=/etc/rancher/k3s/k3s.yaml
        echo 'export KUBECONFIG=/etc/rancher/k3s/k3s.yaml' >> ~/.bashrc
    fi
    
    log_info "✅ Kubernetes依赖安装完成"
}

# 生成Helm values配置
generate_helm_values() {
    log_info "生成Helm values配置..."
    
    local values_file="$PROJECT_DIR/configs/values-external.yaml"
    mkdir -p "$(dirname "$values_file")"
    
    cat > "$values_file" << EOF
# ESS-HELM 外部服务器配置
# 基于官方element-hq/ess-helm ${OFFICIAL_CHART_VERSION}

global:
  serverName: "$SERVER_NAME"

synapse:
  enabled: true
  serverName: "$SERVER_NAME"
  config:
    enableRegistration: false
    registrationRequiresToken: true
  
  # 外部服务器资源配置
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "4Gi"
      cpu: "2000m"

elementWeb:
  enabled: true
  config:
    default_server_config:
      m.homeserver:
        base_url: "https://$SERVER_NAME:$MATRIX_PORT"
        server_name: "$SERVER_NAME"

matrixAuthenticationService:
  enabled: true
  config:
    http:
      public_base: "https://$AUTH_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT"

matrixRtc:
  enabled: true
  livekit:
    enabled: true
    # 虚拟IP配置
    config:
      rtc:
        port_range_start: 30152
        port_range_end: 33152
        use_external_ip: true
        external_ip: "$LIVEKIT_VIRTUAL_IP"

postgresql:
  enabled: true
  auth:
    database: "synapse"
    username: "synapse_user"
  primary:
    persistence:
      size: "50Gi"

# 外部服务器网络配置
service:
  type: LoadBalancer
  ports:
    https: $HTTPS_PORT
    matrix: $MATRIX_PORT

# Ingress配置
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: $ELEMENT_SUBDOMAIN.$MAIN_DOMAIN
      paths:
        - path: /
          pathType: Prefix
    - host: $SERVER_NAME
      paths:
        - path: /
          pathType: Prefix

# Router WAN IP检测配置
routerWanIpDetection:
  enabled: true
  router:
    host: "$ROUTER_IP"
    username: "$ROUTER_USERNAME"
    password: "$ROUTER_PASSWORD"
    interface: "$WAN_INTERFACE"
  detection:
    interval: 5  # 5秒检测间隔

# 虚拟IP路由配置
virtualIpRouting:
  enabled: true
  livekit:
    virtualIp: "$LIVEKIT_VIRTUAL_IP"
  turn:
    virtualIp: "$TURN_VIRTUAL_IP"
EOF

    log_info "✅ Helm values配置生成完成"
}

# 部署ESS-HELM
deploy_ess_helm() {
    log_info "部署ESS-HELM..."
    
    local namespace="ess"
    local release_name="ess"
    local chart_path="$PROJECT_DIR/charts/matrix-stack"
    local values_file="$PROJECT_DIR/configs/values-external.yaml"
    
    # 添加Helm仓库
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo update
    
    # 创建命名空间
    kubectl create namespace "$namespace" --dry-run=client -o yaml | kubectl apply -f -
    
    # 部署ESS-HELM
    helm upgrade --install "$release_name" "$chart_path" \
        --namespace "$namespace" \
        --values "$values_file" \
        --timeout 15m \
        --wait
    
    log_info "✅ ESS-HELM部署完成"
}

# 启动Router WAN IP检测服务
start_wan_ip_detection() {
    log_info "启动Router WAN IP检测服务..."
    
    # 复制检测脚本
    sudo cp "$SCRIPT_DIR/router-wan-ip-detector.sh" /usr/local/bin/
    sudo chmod +x /usr/local/bin/router-wan-ip-detector.sh
    
    # 创建systemd服务
    sudo tee /etc/systemd/system/ess-router-wan-ip.service > /dev/null << EOF
[Unit]
Description=ESS-HELM Router WAN IP Detector
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/router-wan-ip-detector.sh
Restart=always
RestartSec=10
Environment=CONFIG_FILE=$CONFIG_FILE

[Install]
WantedBy=multi-user.target
EOF

    sudo systemctl daemon-reload
    sudo systemctl enable ess-router-wan-ip
    sudo systemctl start ess-router-wan-ip
    
    log_info "✅ Router WAN IP检测服务已启动"
}

# 配置虚拟IP路由
setup_virtual_ip_routing() {
    log_info "配置虚拟IP路由..."
    
    # 获取默认网络接口
    local default_interface
    default_interface=$(ip route | grep default | awk '{print $5}' | head -1)
    
    if [[ -z "$default_interface" ]]; then
        log_error "无法获取默认网络接口"
        return 1
    fi
    
    # 添加虚拟IP
    sudo ip addr add "$LIVEKIT_VIRTUAL_IP/32" dev "$default_interface" 2>/dev/null || true
    sudo ip addr add "$TURN_VIRTUAL_IP/32" dev "$default_interface" 2>/dev/null || true
    
    # 配置iptables NAT规则
    local wan_ip
    if [[ -f "/tmp/ess-current-wan-ip" ]]; then
        wan_ip=$(cat "/tmp/ess-current-wan-ip")
        
        # LiveKit NAT规则
        sudo iptables -t nat -A PREROUTING -d "$LIVEKIT_VIRTUAL_IP" -j DNAT --to-destination "$wan_ip" 2>/dev/null || true
        sudo iptables -t nat -A POSTROUTING -s "$wan_ip" -j SNAT --to-source "$LIVEKIT_VIRTUAL_IP" 2>/dev/null || true
        
        # TURN NAT规则
        sudo iptables -t nat -A PREROUTING -d "$TURN_VIRTUAL_IP" -j DNAT --to-destination "$wan_ip" 2>/dev/null || true
        sudo iptables -t nat -A POSTROUTING -s "$wan_ip" -j SNAT --to-source "$TURN_VIRTUAL_IP" 2>/dev/null || true
        
        log_info "✅ 虚拟IP路由配置完成"
    else
        log_warn "WAN IP尚未检测到，虚拟IP路由将在检测到后自动配置"
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    local namespace="ess"
    
    # 检查Pod状态
    log_info "检查Pod状态..."
    kubectl get pods -n "$namespace"
    
    # 检查服务状态
    log_info "检查服务状态..."
    kubectl get svc -n "$namespace"
    
    # 检查系统服务状态
    log_info "检查系统服务状态..."
    sudo systemctl status ess-router-wan-ip --no-pager || true
    
    log_info "✅ 部署验证完成"
}

# 显示部署结果
show_deployment_result() {
    echo
    echo -e "${GREEN}🎉 ESS-HELM 外部服务器部署完成！${NC}"
    echo
    echo -e "${BLUE}=== 访问信息 ===${NC}"
    echo "Matrix服务器: https://$SERVER_NAME:$MATRIX_PORT"
    echo "Element Web: https://$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT"
    echo "认证服务: https://$AUTH_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT"
    echo
    echo -e "${BLUE}=== 管理工具 ===${NC}"
    echo "管理控制台: $SCRIPT_DIR/admin.sh"
    echo "配置文件: $CONFIG_FILE"
    echo
    echo -e "${BLUE}=== 系统服务 ===${NC}"
    echo "Router WAN IP检测: sudo systemctl status ess-router-wan-ip"
    echo
    echo -e "${BLUE}=== 虚拟IP配置 ===${NC}"
    echo "LiveKit虚拟IP: $LIVEKIT_VIRTUAL_IP"
    echo "TURN虚拟IP: $TURN_VIRTUAL_IP"
    echo
    echo -e "${YELLOW}注意: 请确保防火墙已开放相应端口，并配置DNS解析${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}=== ESS-HELM 外部服务器部署开始 ===${NC}"
    
    # 加载和验证配置
    load_config
    validate_config
    
    # 安装依赖
    install_k8s_dependencies
    
    # 生成配置
    generate_helm_values
    
    # 部署ESS-HELM
    deploy_ess_helm
    
    # 启动专用服务
    start_wan_ip_detection
    setup_virtual_ip_routing
    
    # 验证部署
    verify_deployment
    
    # 显示结果
    show_deployment_result
    
    echo -e "${GREEN}✅ 外部服务器部署流程完成${NC}"
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
