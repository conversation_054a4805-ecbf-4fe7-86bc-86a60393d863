{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.matrixAuthenticationService -}}
{{- if .enabled -}}
{{- if $.Capabilities.APIVersions.Has "monitoring.coreos.com/v1/ServiceMonitor" }}
{{- if .serviceMonitors.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    {{- include "element-io.matrix-authentication-service.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-matrix-authentication-service
  namespace: {{ $.Release.Namespace }}
spec:
  endpoints:
  - interval: 30s
    port: internal
  selector:
    matchLabels:
      app.kubernetes.io/instance: "{{ $.Release.Name }}-matrix-authentication-service"
{{- end }}
{{- end }}
{{- end -}}
{{- end -}}
