{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.matrixAuthenticationService -}}
{{- if and .enabled .syn2mas.enabled (not .syn2mas.dryRun) -}}
{{- with .syn2mas -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ $.Release.Name }}-syn2mas
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "element-io.syn2mas.labels" (dict "root" $ "context" .) | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
{{- /*
Hook Weights are
- -10 : The initSecret hook generating secrets used by the syn2mas job
- -5 : The MAS & synapse secret & configMap for the hook, so that they are created before the job
- 0 : The job itself, so that it is run after the secrets and configs are created
*/}}
    "helm.sh/hook-weight": "0"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ $.Release.Name }}-syn2mas
subjects:
- kind: ServiceAccount
  name: {{ include "element-io.ess-library.serviceAccountName" (dict "root" $ "context" (dict "serviceAccount" .serviceAccount "nameSuffix" "syn2mas")) }}
  namespace: {{ $.Release.Namespace }}
{{- end -}}
{{- end -}}
{{- end -}}
