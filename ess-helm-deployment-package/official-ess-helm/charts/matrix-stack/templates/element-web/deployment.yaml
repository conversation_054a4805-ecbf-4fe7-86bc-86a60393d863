{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}
{{- with .Values.elementWeb -}}
{{- if .enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
{{- with .annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
{{- end }}
  labels:
    {{- include "element-io.element-web.labels" (dict "root" $ "context" .) | nindent 4 }}
    k8s.element.io/element-web-config-hash: "{{( include "element-io.element-web.configmap-data" (dict "root" $ "context" .)) | sha1sum }}"
    k8s.element.io/nginx-config-hash: "{{ include "element-io.element-web.nginx-configmap-data" (dict "root" $) | sha1sum }}"
  name: {{ $.Release.Name }}-element-web
  namespace: {{ $.Release.Namespace }}
spec:
  {{ include "element-io.ess-library.deployments.commonSpec" (dict "root" $ "context" (dict "replicas" .replicas "nameSuffix" "element-web")) | nindent 2 }}
  template:
    metadata:
      labels:
        {{- include "element-io.element-web.labels" (dict "root" $ "context" (dict "image" .image "labels" .labels "withChartVersion" false)) | nindent 8 }}
        k8s.element.io/element-web-config-hash: "{{( include "element-io.element-web.configmap-data" (dict "root" $ "context" .)) | sha1sum }}"
        k8s.element.io/nginx-config-hash: "{{ include "element-io.element-web.nginx-configmap-data" (dict "root" $) | sha1sum }}"
{{- with .annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
{{- end }}
    spec:
{{- include "element-io.ess-library.pods.commonSpec" (dict "root" $ "context" (dict "componentValues" . "instanceSuffix" "element-web" "deployment" true)) | nindent 6 }}
      containers:
      - name: element-web
{{- with .image -}}
{{- if .digest }}
        image: "{{ .registry }}/{{ .repository }}@{{ .digest }}"
        imagePullPolicy: {{ .pullPolicy | default "IfNotPresent" }}
{{- else }}
        image: "{{ .registry }}/{{ .repository }}:{{ .tag }}"
        imagePullPolicy: {{ .pullPolicy | default "Always" }}
{{- end }}
{{- end }}
        {{- include "element-io.ess-library.pods.env" (dict "root" $ "context" (dict "componentValues" . "componentName" "element-web")) | nindent 8 }}
{{- with .containersSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
{{- end }}
        ports:
        - containerPort: 8080
          name: element
          protocol: TCP
        livenessProbe: {{- include "element-io.ess-library.pods.probe" .livenessProbe | nindent 10 }}
          httpGet:
            path: /health
            port: element
            scheme: HTTP
        readinessProbe: {{- include "element-io.ess-library.pods.probe" .readinessProbe | nindent 10 }}
          httpGet:
            path: /health
            port: element
            scheme: HTTP
        startupProbe: {{- include "element-io.ess-library.pods.probe" .startupProbe | nindent 10 }}
          httpGet:
            path: /health
            port: element
            scheme: HTTP
{{- with .resources }}
        resources:
          {{- toYaml . | nindent 10 }}
{{- end }}
        volumeMounts:
        - mountPath: /app/config.json
          name: config
          readOnly: true
          subPath: config.json
        - mountPath: /etc/nginx/conf.d/default.conf
          name: nginx-config
          readOnly: true
          subPath: default.conf
        - mountPath: /etc/nginx/conf.d/http_customisations.conf
          name: nginx-config
          readOnly: true
          subPath: http_customisations.conf
        - mountPath: /etc/nginx/security_headers.conf
          name: nginx-config
          readOnly: true
          subPath: security_headers.conf
        - mountPath: /tmp
          name: nginx-tmp
      restartPolicy: Always
      volumes:
      - configMap:
          defaultMode: 420
          name: {{ $.Release.Name }}-element-web
        name: config
      - configMap:
          defaultMode: 420
          name: {{ $.Release.Name }}-element-web-nginx
        name: nginx-config
      - emptyDir:
          medium: Memory
        name: nginx-tmp
{{- end }}
{{- end }}
