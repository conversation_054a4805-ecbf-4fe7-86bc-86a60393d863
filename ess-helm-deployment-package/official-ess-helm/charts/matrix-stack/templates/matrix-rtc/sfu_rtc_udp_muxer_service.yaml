{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.matrixRTC -}}
{{- if .enabled -}}
{{- with .sfu -}}
{{- if and .enabled .exposedServices.rtcMuxedUdp.enabled (eq .exposedServices.rtcMuxedUdp.portType "NodePort") -}}
apiVersion: v1
kind: Service
metadata:
  labels:
    {{- include "element-io.matrix-rtc-sfu-rtc.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-matrix-rtc-sfu-muxed-udp
  namespace: {{ $.Release.Namespace }}
spec:
  type: NodePort
  externalTrafficPolicy: Local
  ports:
  - name: "rtc-muxed-udp"
    protocol: "UDP"
    port: {{ .exposedServices.rtcMuxedUdp.port }}
    targetPort: {{ .exposedServices.rtcMuxedUdp.port }}
    nodePort: {{ .exposedServices.rtcMuxedUdp.port }}
  selector:
    app.kubernetes.io/instance: "{{ $.Release.Name }}-matrix-rtc-sfu"
{{- end }}
{{- end }}
{{- end }}
{{- end }}
