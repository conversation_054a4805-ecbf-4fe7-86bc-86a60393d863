{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.matrixRTC -}}
{{- if .enabled -}}
{{- if $.Capabilities.APIVersions.Has "monitoring.coreos.com/v1/ServiceMonitor" }}
{{- if .serviceMonitors.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    {{- include "element-io.matrix-rtc-authorisation-service.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-matrix-rtc-authorisation-service
  namespace: {{ $.Release.Namespace }}
spec:
  endpoints:
  - interval: 30s
    port: http
  selector:
    matchLabels:
      app.kubernetes.io/part-of: matrix-stack
      app.kubernetes.io/component: matrix-rtc-authorisation-service
      app.kubernetes.io/instance: {{ $.Release.Name }}-matrix-rtc-authorisation-service
{{- end }}
{{- end }}
{{- end -}}
{{- end -}}
