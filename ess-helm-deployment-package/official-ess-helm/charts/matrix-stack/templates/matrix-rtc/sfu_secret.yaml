{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.matrixRTC -}}
{{- if and .enabled .sfu.enabled -}}
apiVersion: v1
kind: Secret
type: Opaque
metadata:
  labels:
    {{- include "element-io.matrix-rtc-sfu.labels" (dict "root" $ "context" .sfu) | nindent 4 }}
  name: {{ $.Release.Name }}-matrix-rtc-sfu
  namespace: {{ $.Release.Namespace }}
data:
  {{- include "element-io.matrix-rtc-sfu.secret-data" (dict "root" $ "context" .) | nindent 2 -}}
{{- end }}
{{- end }}
