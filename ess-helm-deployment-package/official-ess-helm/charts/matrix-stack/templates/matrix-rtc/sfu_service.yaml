{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.matrixRTC -}}
{{- if .enabled -}}
{{- with .sfu -}}
{{- if .enabled -}}
apiVersion: v1
kind: Service
metadata:
  labels:
    {{- include "element-io.matrix-rtc-sfu.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-matrix-rtc-sfu
  namespace: {{ $.Release.Namespace }}
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 7880
    targetPort: http
  selector:
    app.kubernetes.io/instance: "{{ $.Release.Name }}-matrix-rtc-sfu"
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
