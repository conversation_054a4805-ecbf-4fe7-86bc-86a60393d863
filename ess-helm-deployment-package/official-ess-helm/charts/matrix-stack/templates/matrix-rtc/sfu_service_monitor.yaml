{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.matrixRTC -}}
{{- if .enabled -}}
{{- with .sfu -}}
{{- if .enabled -}}
{{- if $.Capabilities.APIVersions.Has "monitoring.coreos.com/v1/ServiceMonitor" }}
{{- if .serviceMonitors.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    {{- include "element-io.matrix-rtc-sfu.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-matrix-rtc-sfu
  namespace: {{ $.Release.Namespace }}
spec:
  endpoints:
  - interval: 30s
    port: http
  selector:
    matchLabels:
      app.kubernetes.io/part-of: matrix-stack
      app.kubernetes.io/component: matrix-rtc-voip-server
      app.kubernetes.io/instance: {{ $.Release.Name }}-matrix-rtc-sfu
{{- end }}
{{- end }}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
