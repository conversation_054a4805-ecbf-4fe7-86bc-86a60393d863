{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}
{{- with .Values.initSecrets -}}
{{- if and .enabled .rbac.create (include "element-io.init-secrets.generated-secrets" (dict "root" $)) -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ $.Release.Name }}-init-secrets
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "element-io.init-secrets.labels" (dict "root" $ "context" .) | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-10"
rules:
{{/*
  https://kubernetes.io/docs/reference/access-authn-authz/rbac/#referring-to-resources
  You cannot restrict create or deletecollection requests by resourceName.
  For create, this limitation is because the object name is not known at authorization time.
*/}}
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["create"]
- apiGroups: [""]
  resources: ["secrets"]
  resourceNames: [ "{{ $.Release.Name }}-generated" ]
  verbs: ["get", "update"]
{{- end -}}
{{- end -}}
