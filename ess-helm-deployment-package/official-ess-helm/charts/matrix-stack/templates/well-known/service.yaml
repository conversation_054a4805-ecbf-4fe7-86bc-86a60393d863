{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.wellKnownDelegation -}}
{{- if .enabled -}}
apiVersion: v1
kind: Service
metadata:
  labels:
    {{- include "element-io.well-known-delegation-ingress.labels" (dict "root" $ "context" $.Values.haproxy) | nindent 4 }}
  name: {{ $.Release.Name }}-well-known
  namespace: {{ $.Release.Namespace }}
spec:
  type: {{ .ingress.service.type | default $.Values.ingress.service.type }}
  ports:
  - name: haproxy-wkd
    port: 8010
    targetPort: haproxy-wkd
  selector:
    app.kubernetes.io/instance: "{{ $.Release.Name }}-haproxy"
{{- end -}}
{{- end -}}
