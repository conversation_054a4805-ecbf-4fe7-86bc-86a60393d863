{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- if $.Values.synapse.enabled -}}
{{- with .Values.synapse -}}
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "element-io.synapse.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-synapse-haproxy
  namespace: {{ $.Release.Namespace }}
data:
{{- include "element-io.synapse-haproxy.configmap-data" (dict "root" $) | nindent 2 -}}
{{- end -}}
{{- end -}}
