{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if .enabled -}}
{{- $enabledWorkers := (include "element-io.synapse.enabledWorkers" (dict "root" $)) | fromJson }}
{{- range $processType, $unmergedProcessDetails := mustMergeOverwrite (dict "main" dict) $enabledWorkers }}
{{- with (mustMergeOverwrite ($.Values.synapse | deepCopy) ($unmergedProcessDetails | deepCopy) (dict "processType" $processType "isHook" false)) }}
{{- $workerTypeName := include "element-io.synapse.process.workerTypeName" (dict "root" $ "context" $processType) }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
{{- with .annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
{{- end }}
  labels:
    {{- include "element-io.synapse.process.labels" (dict "root" $ "context" .) | nindent 4 }}
    k8s.element.io/synapse-config-hash: "{{ include "element-io.synapse.configmap-data"  (dict "root" $ "context" .) | sha1sum }}"
    k8s.element.io/synapse-secret-hash: "{{ include "element-io.synapse.secret-data"  (dict "root" $ "context" .) | sha1sum }}"
{{- range $index, $appservice := .appservices }}
{{- if .configMap }}
    k8s.element.io/as-registration-{{ $index }}-hash: "{{ (lookup "v1" "ConfigMap" $.Release.Namespace (tpl $appservice.configMap $)) | toJson | sha1sum }}"
{{- else }}
    k8s.element.io/as-registration-{{ $index }}-hash: "{{ (lookup "v1" "Secret" $.Release.Namespace (tpl $appservice.secret $)) | toJson | sha1sum }}"
{{- end }}
{{- end }}
    {{ include "element-io.ess-library.postgres-label" (dict "root" $ "context" (dict
                                                            "essPassword" "synapse"
                                                            "postgresProperty" .postgres
                                                            )
                                        ) }}
  name: {{ $.Release.Name }}-synapse-{{ $workerTypeName }}
  namespace: {{ $.Release.Namespace }}
spec:
  serviceName: {{ $.Release.Name }}-synapse-{{ $workerTypeName }}
  replicas: {{ .replicas | default 1 }}
  selector:
    matchLabels:
      app.kubernetes.io/instance: {{ $.Release.Name }}-synapse-{{ $processType }}
  updateStrategy:
    type: RollingUpdate
  # Without this CrashLoopBackoffs due to config failures block pod recreation
  podManagementPolicy: Parallel
  {{- include "element-io.synapse.pod-template" (dict "root" $ "context" .) | nindent 2 }}
---
{{- end }}
{{- end }}
{{- end }}
{{- end -}}
