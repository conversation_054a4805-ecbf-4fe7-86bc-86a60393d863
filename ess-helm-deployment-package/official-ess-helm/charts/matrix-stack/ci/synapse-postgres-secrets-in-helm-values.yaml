# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: synapse-minimal.yaml synapse-secrets-in-helm.yaml postgres-secrets-in-helm.yaml postgres-synapse-secrets-in-helm.yaml init-secrets-disabled.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

elementWeb:
  enabled: false
initSecrets:
  enabled: false
matrixAuthenticationService:
  enabled: false
matrixRTC:
  enabled: false
postgres:
  adminPassword:
    value: CHANGEME-phiaPh8iu9tiivaiWahquaeg8ohcub4a
  essPasswords:
    synapse:
      value: CHANGEME-yuhaoshiupahmaGheiheiloJuone3aim
serverName: ess.localhost
synapse:
  ingress:
    host: synapse.ess.localhost
  macaroon:
    value: CHANGEME-eek3Eigoh8ux8laeTingeej1
  registrationSharedSecret:
    value: CHANGEME-ooWo6je<PERSON>hhei3Hae0eer9U
  signingKey:
    value: ed25519 0 bNQOzBUDszff7Ax81z6w0uZ1IPWoxYaazT7emaZEfpw
wellKnownDelegation:
  enabled: false
