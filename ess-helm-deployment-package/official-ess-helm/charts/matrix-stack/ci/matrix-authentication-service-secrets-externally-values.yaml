# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: matrix-authentication-service-minimal.yaml matrix-authentication-service-additional-secrets-externally.yaml matrix-authentication-service-postgres.yaml matrix-authentication-service-postgres-secrets-externally.yaml matrix-authentication-service-secrets-externally.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  enabled: false
matrixAuthenticationService:
  additional:
    example-value:
      configSecret: '{{ $.Release.Name }}-mas-external'
      configSecretKey: config
  encryptionSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: encryption
  ingress:
    host: mas.ess.localhost
  postgres:
    database: mas
    host: postgres
    password:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: postgresPassword
    user: mas
  privateKeys:
    ecdsaPrime256v1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaPrime256v1
    ecdsaSecp256k1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaSecp256k1
    ecdsaSecp384r1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaSecp384r1
    rsa:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysRSA
  synapseOIDCClientSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: synapseOIDC
  synapseSharedSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: synapseShared
matrixRTC:
  enabled: false
synapse:
  enabled: false
wellKnownDelegation:
  enabled: false
