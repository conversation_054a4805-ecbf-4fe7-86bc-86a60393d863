# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: synapse-minimal.yaml synapse-secrets-externally.yaml postgres-secrets-externally.yaml postgres-synapse-secrets-externally.yaml init-secrets-disabled.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

elementWeb:
  enabled: false
initSecrets:
  enabled: false
matrixAuthenticationService:
  enabled: false
matrixRTC:
  enabled: false
postgres:
  adminPassword:
    secret: '{{ $.Release.Name }}-pg-external'
    secretKey: adminPasswordShared
  essPasswords:
    synapse:
      secret: '{{ $.Release.Name }}-pg-external'
      secretKey: synapsePasswordShared
serverName: ess.localhost
synapse:
  appservices:
    - secret: '{{ $.Release.Name }}-synapse-external'
      secretKey: bridge_registration.yaml
  ingress:
    host: synapse.ess.localhost
  macaroon:
    secret: '{{ $.Release.Name }}-synapse-external'
    secretKey: macaroon
  registrationSharedSecret:
    secret: '{{ $.Release.Name }}-synapse-external'
    secretKey: registrationSharedSecret
  signingKey:
    secret: '{{ $.Release.Name }}-synapse-external'
    secretKey: signingKey
wellKnownDelegation:
  enabled: false
