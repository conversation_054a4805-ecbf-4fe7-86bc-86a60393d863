# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: matrix-authentication-service-minimal.yaml matrix-authentication-service-secrets-externally.yaml postgres-secrets-externally.yaml postgres-matrix-authentication-service-secrets-externally.yaml init-secrets-disabled.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

elementWeb:
  enabled: false
initSecrets:
  enabled: false
matrixAuthenticationService:
  encryptionSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: encryption
  ingress:
    host: mas.ess.localhost
  privateKeys:
    ecdsaPrime256v1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaPrime256v1
    ecdsaSecp256k1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaSecp256k1
    ecdsaSecp384r1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaSecp384r1
    rsa:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysRSA
  synapseOIDCClientSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: synapseOIDC
  synapseSharedSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: synapseShared
matrixRTC:
  enabled: false
postgres:
  adminPassword:
    secret: '{{ $.Release.Name }}-pg-external'
    secretKey: adminPasswordShared
  essPasswords:
    matrixAuthenticationService:
      secret: '{{ $.Release.Name }}-pg-external'
      secretKey: masPasswordShared
synapse:
  enabled: false
wellKnownDelegation:
  enabled: false
