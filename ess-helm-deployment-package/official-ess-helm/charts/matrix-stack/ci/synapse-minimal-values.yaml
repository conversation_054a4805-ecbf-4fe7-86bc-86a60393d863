# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: synapse-minimal.yaml init-secrets-minimal.yaml postgres-minimal.yaml deployment-markers-minimal.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  enabled: false
matrixAuthenticationService:
  enabled: false
matrixRTC:
  enabled: false
serverName: ess.localhost
synapse:
  ingress:
    host: synapse.ess.localhost
wellKnownDelegation:
  enabled: false
