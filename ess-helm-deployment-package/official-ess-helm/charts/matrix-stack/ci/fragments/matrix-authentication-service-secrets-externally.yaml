# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

matrixAuthenticationService:

  synapseSharedSecret:
    secret: "{{ $.Release.Name }}-mas-external"
    secretKey: synapseShared

  synapseOIDCClientSecret:
    secret: "{{ $.Release.Name }}-mas-external"
    secretKey: synapseOIDC

  encryptionSecret:
    secret: "{{ $.Release.Name }}-mas-external"
    secretKey: encryption

  privateKeys:
    rsa:
      secret: "{{ $.Release.Name }}-mas-external"
      secretKey: keysRSA
    ecdsaPrime256v1:
      secret: "{{ $.Release.Name }}-mas-external"
      secretKey: keysEcdsaPrime256v1
    ecdsaSecp256k1:
      secret: "{{ $.Release.Name }}-mas-external"
      secretKey: keysEcdsaSecp256k1
    ecdsaSecp384r1:
      secret: "{{ $.Release.Name }}-mas-external"
      secretKey: keysEcdsaSecp384r1
