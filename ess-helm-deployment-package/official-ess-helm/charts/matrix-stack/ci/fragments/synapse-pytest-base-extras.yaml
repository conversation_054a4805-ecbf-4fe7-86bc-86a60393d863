# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

synapse:
  ingress:
    host: synapse.{{ $.Values.serverName }}
    tlsSecret: "{{ $.Release.Name }}-synapse-web-tls"

  checkConfigHook:
    annotations:
      has-no-service-monitor: "true"

  redis:
    annotations:
      has-no-service-monitor: "true"

    podSecurityContext:
      runAsGroup: 0

  podSecurityContext:
    runAsGroup: 0

  extraArgs:
  # Validate that any Synapse config that has a <foo>_path equivalent uses it
  - --no-secrets-in-config

haproxy:
  replicas: 2
  podSecurityContext:
    runAsGroup: 0

postgres:
  podSecurityContext:
    runAsGroup: 0
