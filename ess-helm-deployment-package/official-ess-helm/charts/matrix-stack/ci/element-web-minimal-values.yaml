# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: element-web-minimal.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  ingress:
    host: element.ess.localhost
matrixAuthenticationService:
  enabled: false
matrixRTC:
  enabled: false
synapse:
  enabled: false
wellKnownDelegation:
  enabled: false
