{#
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
#}

{% import "common/sub_schema_values.yaml.j2" as sub_schema_values %}
enabled: true
{{- sub_schema_values.image(registry='ghcr.io', repository='element-hq/matrix-authentication-service', tag='0.17.1') }}

replicas: 1

{{ sub_schema_values.postgresLibPQ() }}

{{ sub_schema_values.credential("Encryption secret", "encryptionSecret", initIfAbsent=True) }}
{{ sub_schema_values.credential("Synapse - MAS Shared Secret", "synapseSharedSecret", initIfAbsent=True) }}
{{ sub_schema_values.credential("Synapse - MAS OIDC Client Secret", "synapseOIDCClientSecret", initIfAbsent=True) }}

## Additional configuration to provide to Matrix Authentication Service.
## Each key under additional is an additional config to merge into Matrix Authentication Service config.yaml
## Full details on available configuration options can be found at https://element-hq.github.io/matrix-authentication-service/reference/configuration.html
{{- sub_schema_values.additionalConfig() }}
privateKeys:
{{ sub_schema_values.credential("RSA Private Key", "rsa", initIfAbsent=True) | indent(2) }}
{{ sub_schema_values.credential("ECDSA Prime256v1 Private Key", "ecdsaPrime256v1", initIfAbsent=True) | indent(2) }}
{{ sub_schema_values.credential("ECDSA Secp256k1 Private Key", "ecdsaSecp256k1") | indent(2) }}
{{ sub_schema_values.credential("ECDSA Secp384r1 Private Key", "ecdsaSecp384r1") | indent(2) }}

{{ sub_schema_values.ingress() }}
{{ sub_schema_values.resources(requests_memory='50Mi', requests_cpu='50m', limits_memory='350Mi') }}
{{ sub_schema_values.labels() }}
{{ sub_schema_values.serviceAccount() }}
{{ sub_schema_values.nodeSelector() }}
{{ sub_schema_values.tolerations() }}
{{ sub_schema_values.hostAliases() }}
{{ sub_schema_values.topologySpreadConstraints() }}
{{ sub_schema_values.podSecurityContext(user_id=10005, group_id=10005) }}
{{ sub_schema_values.containersSecurityContext() }}
{{ sub_schema_values.workloadAnnotations() }}
{{ sub_schema_values.serviceMonitors() }}
{{ sub_schema_values.extraEnv() }}
{{ sub_schema_values.probe("liveness") }}
{{ sub_schema_values.probe("readiness") }}
{{ sub_schema_values.probe("startup", failureThreshold=4) }}

## Enables synapse to Matrix Authentication Service migration
## The migration process must follow the following steps:
## 1. Set `matrixAuthenticationService.enabled: true` and `matrixAuthenticationService.syn2mas.enable: true`, run the  helm upgrade command, check the result of the pre-upgrade hook job
## 2. Set `matrixAuthenticationService.syn2mas.dryRun`: false, run the helm upgrade command, check the result of the pre-upgrade hook job
## 3. Set `matrixAuthenticationService.syn2mas.enabled: false` to disable syn2mas and allow running `helm upgrade` again.
##
## If `deploymentMarkers` are enabled, following the steps above will cause the following `MATRIX_STACK_MSC3861` marker transitions :
## 1. Stay on `legacy_auth`
## 2. `legacy_auth` -> `syn2mas_migrated`
## 3. `syn2mas_migrated` -> `delegated_auth`
## The marker will effectively prevent :
## - Running syn2mas migration again after it has run successfully and is in `syn2mas_migrated` state
## - Downgrading from `syn2mas_migrated`/`delegated_auth` to `legacy_auth`
syn2mas:
  enabled: false

  # Syn2Mas relies on the debug image to copy mas-cli to the matrix-tools container
  {{- sub_schema_values.image(registry='ghcr.io', repository='element-hq/matrix-authentication-service', tag='0.17.1-debug') | indent(2) }}
  {{- sub_schema_values.labels() | indent(2) -}}
  {{- sub_schema_values.workloadAnnotations() | indent(2) -}}
  {{- sub_schema_values.containersSecurityContext() | indent(2) -}}
  {{- sub_schema_values.nodeSelector() | indent(2) -}}
  {{- sub_schema_values.podSecurityContext(user_id='10005', group_id='10005') | indent(2) -}}
  {{- sub_schema_values.resources(requests_memory='50Mi', requests_cpu='50m', limits_memory='350Mi') | indent(2) -}}
  {{- sub_schema_values.serviceAccount() | indent(2) -}}
  {{- sub_schema_values.extraEnv() | indent(2) -}}
  {{- sub_schema_values.tolerations() | indent(2) }}

  ## Runs the syn2mas process in dryRun mode.
  ## Force the authentication to happen with legacy authentication.
  ## This can be used to deploy Matrix Authentication Service and keeping auth on Synapse.
  ## This must be set to false and never switch again after the migration to MAS has been run or the deployment markers hooks will prevent redeploying.
  dryRun: true
