{"type": "object", "additionalProperties": {"type": "object", "oneOf": [{"required": ["config"], "not": {"required": ["config<PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, {"required": ["config<PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON><PERSON><PERSON>"], "not": {"required": ["config"]}}], "properties": {"properties": {"config": {"type": "string"}, "configSecret": {"type": "string"}, "configSecretKey": {"type": "string"}}}}}