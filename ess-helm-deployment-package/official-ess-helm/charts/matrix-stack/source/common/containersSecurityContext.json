{"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object"}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object"}}, "type": "object"}