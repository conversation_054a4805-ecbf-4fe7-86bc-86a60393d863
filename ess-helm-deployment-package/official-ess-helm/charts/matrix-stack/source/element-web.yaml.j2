{#
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
#}


{% import 'sub_schema_values.yaml.j2' as sub_schema_values -%}

enabled: true

## Arbitrary extra config to inject into Element Web's config.json.
## Each key under additional is an additional config to merge into Element Web's config.json.
##
## Full details on available configuration options can be found at https://github.com/element-hq/element-web/blob/develop/docs/config.md
## Most settings are configurable but some settings are owned by the chart and can't be overwritten
additional: {}

# Number of Element Web replicas to start up
replicas: 1
{{- sub_schema_values.image(registry='ghcr.io', repository='element-hq/element-web', tag='v1.11.104') -}}
{{- sub_schema_values.ingress() -}}
{{- sub_schema_values.labels() -}}
{{- sub_schema_values.workloadAnnotations() -}}
{{- sub_schema_values.extraEnv() -}}
{{- sub_schema_values.containersSecurityContext() -}}
{{- sub_schema_values.nodeSelector() -}}
{{- sub_schema_values.podSecurityContext(user_id='10004', group_id='10004') -}}
{{- sub_schema_values.resources(requests_memory='50Mi', requests_cpu='50m', limits_memory='200Mi') -}}
{{- sub_schema_values.serviceAccount() -}}
{{- sub_schema_values.tolerations() -}}
{{- sub_schema_values.topologySpreadConstraints() }}
{{- sub_schema_values.probe("liveness") }}
{{- sub_schema_values.probe("readiness", periodSeconds=3) }}
{{- sub_schema_values.probe("startup", failureThreshold=4, periodSeconds=3) }}
