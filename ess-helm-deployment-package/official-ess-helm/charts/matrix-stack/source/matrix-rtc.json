{"$id": "file://matrix-rtc", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "livekitAuth": {"type": "object", "oneOf": [{"required": ["keysYaml"], "not": {"required": ["key", "secret"]}}, {"required": ["key", "secret"], "not": {"required": ["keysYaml"]}}], "properties": {"keysYaml": {"$ref": "file://common/credential.json"}, "key": {"type": "string"}, "secret": {"$ref": "file://common/credential.json"}}}, "replicas": {"type": "integer"}, "extraEnv": {"$ref": "file://common/extraEnv.json"}, "image": {"$ref": "file://common/image.json"}, "ingress": {"$ref": "file://common/ingress.json"}, "labels": {"$ref": "file://common/labels.json"}, "hostAliases": {"$ref": "file://common/hostAliases.json"}, "annotations": {"$ref": "file://common/workloadAnnotations.json"}, "containersSecurityContext": {"$ref": "file://common/containersSecurityContext.json"}, "serviceMonitors": {"$ref": "file://common/serviceMonitors.json"}, "nodeSelector": {"$ref": "file://common/nodeSelector.json"}, "podSecurityContext": {"$ref": "file://common/podSecurityContext.json"}, "resources": {"$ref": "file://common/resources.json"}, "serviceAccount": {"$ref": "file://common/serviceAccount.json"}, "tolerations": {"$ref": "file://common/tolerations.json"}, "topologySpreadConstraints": {"$ref": "file://common/topologySpreadConstraints.json"}, "livenessProbe": {"$ref": "file://common/probe.json"}, "readinessProbe": {"$ref": "file://common/probe.json"}, "startupProbe": {"$ref": "file://common/probe.json"}, "sfu": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "image": {"$ref": "file://common/image.json"}, "labels": {"$ref": "file://common/labels.json"}, "extraEnv": {"$ref": "file://common/extraEnv.json"}, "annotations": {"$ref": "file://common/workloadAnnotations.json"}, "hostNetwork": {"type": "boolean"}, "additional": {"$ref": "file://common/additional.json"}, "logging": {"type": "object", "properties": {"level": {"type": "string", "enum": ["debug", "info", "warn", "error"]}, "pionLevel": {"type": "string", "enum": ["debug", "info", "warn", "error"]}, "json": {"type": "boolean"}}}, "exposedServices": {"type": "object", "properties": {"rtcTcp": {"$ref": "file://common/exposedServicePort.json"}, "rtcMuxedUdp": {"$ref": "file://common/exposedServicePort.json"}, "rtcUdp": {"$ref": "file://common/exposedServicePortRange.json"}}}, "containersSecurityContext": {"$ref": "file://common/containersSecurityContext.json"}, "hostAliases": {"$ref": "file://common/hostAliases.json"}, "nodeSelector": {"$ref": "file://common/nodeSelector.json"}, "podSecurityContext": {"$ref": "file://common/podSecurityContext.json"}, "serviceMonitors": {"$ref": "file://common/serviceMonitors.json"}, "resources": {"$ref": "file://common/resources.json"}, "serviceAccount": {"$ref": "file://common/serviceAccount.json"}, "tolerations": {"$ref": "file://common/tolerations.json"}, "livenessProbe": {"$ref": "file://common/probe.json"}, "readinessProbe": {"$ref": "file://common/probe.json"}, "startupProbe": {"$ref": "file://common/probe.json"}}}}}