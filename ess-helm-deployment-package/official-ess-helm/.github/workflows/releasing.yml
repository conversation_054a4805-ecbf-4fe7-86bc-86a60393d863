# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

name: Helm Chart packaging and releasing
on:
  pull_request_target:
  push:
    branches:
    - main
    tags:
    - '[0-9]+.[0-9]+.[0-9]+'
  workflow_dispatch:

jobs:
  helm-package:
    permissions:
      contents: read
      packages: write
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}

    - uses: matrix-org/setup-python-poetry@5bbf6603c5c930615ec8a29f1b5d7d258d905aa4  # v2
      with:
        poetry-version: "1.8.5"
        python-version: "3.x"

    - name: Load poetry path
      run: |
        echo "$(poetry env info -p)/bin" >> "${GITHUB_PATH}"

    - uses: azure/setup-helm@****************************************  # v4.3.0

    - name: Set version
      run: |
        if [ "$GITHUB_REF_TYPE" = "tag" ]; then
          scripts/check_semver.py "$GITHUB_REF_NAME"
          scripts/set_chart_version.sh "$GITHUB_REF_NAME"
        elif [ "$GITHUB_EVENT_NAME" = "pull_request" ] || [ "$GITHUB_EVENT_NAME" = "pull_request_target" ]; then
            actual_pr_head_commit=$(yq --input-format json -r '.pull_request.head.sha' "$GITHUB_EVENT_PATH")
            version=$(yq '.version' charts/matrix-stack/Chart.yaml | sed "s/-dev/-sha$actual_pr_head_commit/")
            scripts/set_chart_version.sh "$version"
        elif [ "$GITHUB_EVENT_NAME" != "main" ]; then
            version=$(yq '.version' charts/matrix-stack/Chart.yaml | sed "s/-dev/-sha$GITHUB_SHA/")
            scripts/set_chart_version.sh "$version"
        else
          echo "Did not set chart version on event $GITHUB_EVENT_NAME: $GITHUB_REF_TYPE/$GITHUB_REF_NAME"
        fi

    - name: Login to GitHub Container Registry
      uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772  # v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ github.token }}

    - name: Set helm changes annotations
      run: |
        scripts/towncrier_to_helm_annotation.py charts/matrix-stack

    - name: Generate changelog
      run: |
        version=$(yq '.version' charts/matrix-stack/Chart.yaml)
        # Add changes to CHANGELOG.md, keep files to generate CHANGELOG.latest.md
        towncrier build --version "$version" --keep
        # in empty file
        sed -i "s/CHANGELOG.md/CHANGELOG.latest.md/" pyproject.toml
        towncrier build --version "$version" --yes
        sed -i "s/CHANGELOG.latest.md/CHANGELOG.md/" pyproject.toml

    - name: Helm package
      run: |
        cd charts/matrix-stack
        helm package .

        helm push matrix-stack-*.tgz oci://ghcr.io/${{ github.repository }}

    - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02  # v4
      with:
        name: helm-package
        path: |
          charts/matrix-stack/*.tgz*
          CHANGELOG.md
          CHANGELOG.latest.md
        retention-days: 1

  release:
    if: ${{ !failure() && !cancelled() && startsWith(github.ref, 'refs/tags/') }}
    permissions:
      contents: write
      packages: read
      pull-requests: write
    needs:
    - helm-package
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}

    - name: Grab packaged chart
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093  # v4
      with:
        name: helm-package
        path: ./

    - uses: matrix-org/setup-python-poetry@5bbf6603c5c930615ec8a29f1b5d7d258d905aa4  # v2
      with:
        poetry-version: "1.8.5"
        python-version: "3.x"

    - name: Load poetry path
      run: |
        echo "$(poetry env info -p)/bin" >> "${GITHUB_PATH}"

    - name: Calculate versions
      id: versions
      run: |
        this_version=$(find charts/matrix-stack -name 'matrix-stack-*.tgz' | sed 's/.*matrix-stack-//;s/.tgz//')
        next_version=$(echo "$this_version" | sed 's/-dev//' | awk -F'[ .]' '{print $1"."$2"."$3+1"-dev"}')
        scripts/set_chart_version.sh "$next_version"

        echo "this-version=$this_version" >> "$GITHUB_OUTPUT"
        echo "next-version=$next_version" >> "$GITHUB_OUTPUT"

    - name: Release
      uses: softprops/action-gh-release@72f2c25fcb47643c292f7107632f7a47c1df5cd8  # v2
      with:
        draft: true
        body_path: CHANGELOG.latest.md
        files: charts/matrix-stack/matrix-stack*.tgz*
        fail_on_unmatched_files: true
        # This could simply be omitted if we didn't want to test without a tag
        tag_name: "${{ steps.versions.outputs.this-version }}"

    - name: Remove latest changelog
      run: |
        rm CHANGELOG.latest.md
        rm newsfragments/*.md
        touch newsfragments/.gitkeep

    - name: Create PR for next patch version
      uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e  # v7
      with:
        branch: "gha/bump-to-${{ steps.versions.outputs.next-version }}"
        base: "main"
        commit-message: "Bump chart version to ${{ steps.versions.outputs.next-version }}"
        title: "Bump chart version to ${{ steps.versions.outputs.next-version }} after release"
        labels: automated,version-bump
        body: |
          ${{ steps.versions.outputs.this-version }} has just been released.
          This PR prepares the chart & dependencies for the next release:
          ${{ steps.versions.outputs.next-version }}.

          The target branch may be wrong. In which case this PR should be taken over and manually adjusted.
