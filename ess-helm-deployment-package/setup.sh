#!/bin/bash
# ESS-HELM 一键部署主入口脚本
# 基于官方element-hq/ess-helm 25.6.2版本
# 严格遵循官方API规范和最佳实践

set -euo pipefail

# 全局配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$HOME/.ess-helm-config"
OFFICIAL_CHART_VERSION="25.6.2"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                    ESS-HELM 一键部署系统                      ║${NC}"
    echo -e "${BLUE}║                基于官方 element-hq/ess-helm ${OFFICIAL_CHART_VERSION}           ║${NC}"
    echo -e "${BLUE}║                                                              ║${NC}"
    echo -e "${BLUE}║  核心功能:                                                    ║${NC}"
    echo -e "${BLUE}║  • Router WAN IP自动检测 (RouterOS API 8728)                 ║${NC}"
    echo -e "${BLUE}║  • 虚拟公网IP路由高可用 (**********/11)                      ║${NC}"
    echo -e "${BLUE}║  • 基于Synapse Admin API的增强管理                           ║${NC}"
    echo -e "${BLUE}║  • 完全基于官方稳定版本和API规范                              ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${YELLOW}注意: 选项 '0' 始终表示返回上级菜单或退出程序${NC}"
    echo
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    local required_deps=("curl" "wget" "git")
    
    for dep in "${required_deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少必需的系统依赖: ${missing_deps[*]}"
        log_info "正在尝试自动安装..."
        
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y "${missing_deps[@]}"
        elif command -v yum &> /dev/null; then
            sudo yum install -y "${missing_deps[@]}"
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y "${missing_deps[@]}"
        else
            log_error "无法自动安装依赖，请手动安装: ${missing_deps[*]}"
            exit 1
        fi
    fi
    
    log_info "✅ 系统依赖检查完成"
}

# 验证官方Chart版本
verify_official_chart() {
    log_info "验证官方Chart版本..."
    
    local chart_file="$SCRIPT_DIR/charts/matrix-stack/Chart.yaml"
    if [[ ! -f "$chart_file" ]]; then
        log_error "官方Chart文件不存在: $chart_file"
        exit 1
    fi
    
    local chart_version
    chart_version=$(grep "^version:" "$chart_file" | cut -d' ' -f2 | sed 's/-dev//')
    
    if [[ "$chart_version" != "$OFFICIAL_CHART_VERSION" ]]; then
        log_warn "Chart版本不匹配: 期望 $OFFICIAL_CHART_VERSION, 实际 $chart_version"
    else
        log_info "✅ 官方Chart版本验证通过: $OFFICIAL_CHART_VERSION"
    fi
}

# 主菜单
show_main_menu() {
    clear
    echo -e "${BLUE}=== ESS-HELM 部署配置主菜单 ===${NC}"
    echo
    echo "1. 🌐 外部服务器部署 (Router WAN IP检测 + 虚拟IP路由)"
    echo "2. 🏠 内部服务器部署 (内网环境，简化配置)"
    echo "3. ⚙️  配置管理"
    echo "4. 📋 查看当前配置"
    echo "5. 🧪 运行验证测试"
    echo "0. 🚪 退出程序"
    echo
    echo "=================================="
    if [[ -f "$CONFIG_FILE" ]]; then
        echo -e "${GREEN}当前配置: $CONFIG_FILE${NC}"
    else
        echo -e "${YELLOW}尚未创建配置${NC}"
    fi
    echo "=================================="
}

# 收集基础配置
collect_basic_config() {
    echo -e "${BLUE}=== 基础配置收集 ===${NC}"
    echo
    
    # 主域名配置 - 基于Matrix规范
    echo -e "${CYAN}1. Matrix服务器域名配置${NC}"
    read -p "请输入您的Matrix服务器域名 (例: matrix.example.com): " SERVER_NAME
    while [[ ! "$SERVER_NAME" =~ ^[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]$ ]]; do
        log_error "域名格式无效，请重新输入"
        read -p "请输入您的Matrix服务器域名 (例: matrix.example.com): " SERVER_NAME
    done
    
    # 提取主域名
    MAIN_DOMAIN="${SERVER_NAME#*.}"
    if [[ "$SERVER_NAME" == "$MAIN_DOMAIN" ]]; then
        MAIN_DOMAIN="$SERVER_NAME"
    fi
    
    # 子域名配置 - 基于官方最佳实践
    echo
    echo -e "${CYAN}2. 服务子域名配置${NC}"
    echo "基于官方推荐配置，可使用默认值"
    
    read -p "Element Web子域名 [element]: " ELEMENT_SUBDOMAIN
    ELEMENT_SUBDOMAIN="${ELEMENT_SUBDOMAIN:-element}"
    
    read -p "认证服务子域名 [auth]: " AUTH_SUBDOMAIN
    AUTH_SUBDOMAIN="${AUTH_SUBDOMAIN:-auth}"
    
    read -p "RTC服务子域名 [rtc]: " RTC_SUBDOMAIN
    RTC_SUBDOMAIN="${RTC_SUBDOMAIN:-rtc}"
    
    # 端口配置 - 基于官方默认值
    echo
    echo -e "${CYAN}3. 端口配置${NC}"
    echo "基于官方默认端口配置"
    
    read -p "HTTPS端口 [443]: " HTTPS_PORT
    HTTPS_PORT="${HTTPS_PORT:-443}"
    
    read -p "Matrix联邦端口 [8448]: " MATRIX_PORT
    MATRIX_PORT="${MATRIX_PORT:-8448}"
    
    # 服务目录配置
    echo
    echo -e "${CYAN}4. 服务目录配置${NC}"
    read -p "服务主目录路径 [/opt/ess-helm]: " SERVICE_DIR
    SERVICE_DIR="${SERVICE_DIR:-/opt/ess-helm}"
    
    log_info "✅ 基础配置收集完成"
}

# 外部服务器特定配置 - 基于RouterOS API规范
collect_external_config() {
    echo -e "${BLUE}=== 外部服务器配置 ===${NC}"
    echo
    
    # RouterOS API配置 - 基于官方API文档
    echo -e "${CYAN}1. RouterOS API配置${NC}"
    echo "基于MikroTik RouterOS官方API规范 (TCP:8728)"
    
    read -p "RouterOS设备IP地址: " ROUTER_IP
    while [[ ! "$ROUTER_IP" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; do
        log_error "IP地址格式无效"
        read -p "RouterOS设备IP地址: " ROUTER_IP
    done
    
    read -p "RouterOS API用户名: " ROUTER_USERNAME
    read -s -p "RouterOS API密码: " ROUTER_PASSWORD
    echo
    
    read -p "WAN接口名称 [ether1]: " WAN_INTERFACE
    WAN_INTERFACE="${WAN_INTERFACE:-ether1}"
    
    # 虚拟IP配置 - 基于需求文档规范
    echo
    echo -e "${CYAN}2. 虚拟公网IP配置${NC}"
    echo "基于需求文档的虚拟IP规范"
    
    read -p "LiveKit虚拟IP [**********]: " LIVEKIT_VIRTUAL_IP
    LIVEKIT_VIRTUAL_IP="${LIVEKIT_VIRTUAL_IP:-**********}"
    
    read -p "TURN虚拟IP [**********]: " TURN_VIRTUAL_IP
    TURN_VIRTUAL_IP="${TURN_VIRTUAL_IP:-**********}"
    
    log_info "✅ 外部服务器配置收集完成"
}

# 保存配置
save_config() {
    local deployment_type="$1"
    
    log_info "保存配置到 $CONFIG_FILE"
    
    cat > "$CONFIG_FILE" << EOF
# ESS-HELM 部署配置
# 基于官方element-hq/ess-helm ${OFFICIAL_CHART_VERSION}
# 生成时间: $(date)

DEPLOYMENT_TYPE="$deployment_type"
OFFICIAL_CHART_VERSION="$OFFICIAL_CHART_VERSION"

# Matrix服务器配置 - 基于Matrix.org规范
SERVER_NAME="$SERVER_NAME"
MAIN_DOMAIN="$MAIN_DOMAIN"
ELEMENT_SUBDOMAIN="$ELEMENT_SUBDOMAIN"
AUTH_SUBDOMAIN="$AUTH_SUBDOMAIN"
RTC_SUBDOMAIN="$RTC_SUBDOMAIN"

# 端口配置 - 基于官方默认值
HTTPS_PORT="$HTTPS_PORT"
MATRIX_PORT="$MATRIX_PORT"

# 目录配置
SERVICE_DIR="$SERVICE_DIR"
SCRIPT_DIR="$SCRIPT_DIR"

EOF

    if [[ "$deployment_type" == "external" ]]; then
        cat >> "$CONFIG_FILE" << EOF
# RouterOS API配置 - 基于官方API规范
ROUTER_IP="$ROUTER_IP"
ROUTER_USERNAME="$ROUTER_USERNAME"
ROUTER_PASSWORD="$ROUTER_PASSWORD"
WAN_INTERFACE="$WAN_INTERFACE"

# 虚拟IP配置 - 基于需求文档
LIVEKIT_VIRTUAL_IP="$LIVEKIT_VIRTUAL_IP"
TURN_VIRTUAL_IP="$TURN_VIRTUAL_IP"

EOF
    fi
    
    log_info "✅ 配置已保存"
}

# 外部服务器部署
deploy_external() {
    echo -e "${BLUE}=== 外部服务器部署 ===${NC}"
    
    collect_basic_config
    collect_external_config
    save_config "external"
    
    echo
    echo -e "${YELLOW}=== 部署确认 ===${NC}"
    echo "部署类型: 外部服务器"
    echo "Matrix服务器: https://$SERVER_NAME:$MATRIX_PORT"
    echo "Element Web: https://$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT"
    echo "RouterOS设备: $ROUTER_IP (接口: $WAN_INTERFACE)"
    echo "虚拟IP: LiveKit($LIVEKIT_VIRTUAL_IP), TURN($TURN_VIRTUAL_IP)"
    echo
    
    read -p "确认开始部署? (y/n): " confirm
    if [[ "$confirm" == "y" || "$confirm" == "Y" ]]; then
        log_info "调用外部服务器部署脚本..."
        bash "$SCRIPT_DIR/scripts/external.sh"
    else
        log_info "部署已取消"
    fi
}

# 内部服务器部署
deploy_internal() {
    echo -e "${BLUE}=== 内部服务器部署 ===${NC}"
    
    collect_basic_config
    save_config "internal"
    
    echo
    echo -e "${YELLOW}=== 部署确认 ===${NC}"
    echo "部署类型: 内部服务器"
    echo "Matrix服务器: https://$SERVER_NAME:$MATRIX_PORT"
    echo "Element Web: https://$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT"
    echo
    
    read -p "确认开始部署? (y/n): " confirm
    if [[ "$confirm" == "y" || "$confirm" == "Y" ]]; then
        log_info "调用内部服务器部署脚本..."
        bash "$SCRIPT_DIR/scripts/internal.sh"
    else
        log_info "部署已取消"
    fi
}

# 主程序循环
main() {
    show_welcome
    read -p "按回车键继续..."
    
    check_dependencies
    verify_official_chart
    
    while true; do
        show_main_menu
        read -p "请选择操作 [1-5, 0退出]: " choice
        
        case "$choice" in
            1)
                deploy_external
                read -p "按回车键继续..."
                ;;
            2)
                deploy_internal
                read -p "按回车键继续..."
                ;;
            3)
                log_info "配置管理功能开发中..."
                read -p "按回车键继续..."
                ;;
            4)
                if [[ -f "$CONFIG_FILE" ]]; then
                    echo -e "${BLUE}=== 当前配置 ===${NC}"
                    cat "$CONFIG_FILE"
                else
                    log_warn "配置文件不存在"
                fi
                read -p "按回车键继续..."
                ;;
            5)
                if [[ -f "$SCRIPT_DIR/scripts/test.sh" ]]; then
                    bash "$SCRIPT_DIR/scripts/test.sh"
                else
                    log_warn "测试脚本不存在"
                fi
                read -p "按回车键继续..."
                ;;
            0)
                log_info "感谢使用ESS-HELM一键部署系统！"
                exit 0
                ;;
            *)
                log_warn "无效选择，请重试"
                read -p "按回车键继续..."
                ;;
        esac
    done
}

# 执行主程序
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
